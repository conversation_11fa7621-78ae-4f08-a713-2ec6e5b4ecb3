package com.wexl.erp.fees.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.UUID;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Entity
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "fee_payment_details")
public class FeePaymentDetail extends Model {
  @Id @GeneratedValue private UUID id;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "fee_payment_id", nullable = false)
  private FeePayment feePayment;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "fee_head_id", nullable = false)
  private FeeHead feeHead;

  @Column(name = "amount_paid", nullable = false)
  private double amountPaid;
}
