package com.wexl.erp.paymentGateway.types;

import static java.util.UUID.randomUUID;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.paymentGateway.dto.PaymentGatewayDto;
import com.wexl.erp.paymentGateway.dto.PaymentMethod;
import com.wexl.erp.paymentGateway.model.PaymentGatewayDetail;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CashPay implements PaymentGatewayRule {
  @Override
  public boolean supports(PaymentMethod paymentMethod) {
    return PaymentMethod.CASH_PAYMENT.equals(paymentMethod);
  }

  @Override
  public PaymentGatewayDto.Response initiatePayment(
      String orgSlug,
      FeeDto.CollectFeeRequest request,
      FeeHead feeHead,
      PaymentGatewayDetail config) {
    return PaymentGatewayDto.Response.builder().referenceId(randomUUID().toString()).build();
  }

  @Override
  public void verifyPayment(
      String orgSlug,
      String paymentId,
      PaymentGatewayDto.VerifyPaymentRequest verifyPaymentRequest,
      PaymentGatewayDetail config) {
    // No verification needed for cash payments
  }
}
