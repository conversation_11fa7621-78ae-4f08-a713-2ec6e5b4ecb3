package com.wexl.erp.fees.service.fineType;

import com.wexl.erp.fees.model.FeeGroupFeeType;
import com.wexl.erp.fees.model.FeeHead;
import com.wexl.erp.fees.model.FineType;
import org.springframework.stereotype.Component;

@Component
public class PercentageRule implements FineTypeRule {

  @Override
  public boolean supports(FeeGroupFeeType feeHeadFeeType) {
    return FineType.PERCENTAGE.equals(feeHeadFeeType.getFineType());
  }

  @Override
  public Double calculateFine(FeeHead feeHead, FeeGroupFeeType feeGroupFeeType) {
    return feeHead.getFineAmount();
  }
}
