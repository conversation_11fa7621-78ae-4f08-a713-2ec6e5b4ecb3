package com.wexl.retail.student.exam;

import com.wexl.retail.student.answer.ExamAnswer;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ExamAnswerRepository extends JpaRepository<ExamAnswer, Long> {

  Optional<ExamAnswer> findByExamAndQuestionUuidAndType(
      Exam exam, String questionUuid, String type);

  List<ExamAnswer> findByExam(Exam savedExam);
}
