package com.wexl.retail.test.schedule.dto;

import com.wexl.retail.test.schedule.repository.ScheduleTestStudentResponseImpl;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleTestResponse {
  private long id;
  private int duration;
  private long testdefinationId;
  private long startDate;
  private long endDate;
  private String type;
  private String scheduleTestType;
  private String status;
  private String subject;
  private List<String> section;
  private String subjectName;
  private String gradeName;
  private String testName;
  private String message;
  private boolean allStudents;
  private List<Long> students;
  private String teacherName;
  private List<ScheduleTestStudentResponseImpl> assignedStudents;
  private Boolean studentsAttempted;
  private boolean isNotificationSent;
  private boolean isQpGenPresent;
}
