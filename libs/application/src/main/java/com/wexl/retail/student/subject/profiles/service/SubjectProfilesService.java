package com.wexl.retail.student.subject.profiles.service;

import static com.wexl.retail.admin.AdminController.IMPORT_STATUS_COMPLETED;

import com.wexl.retail.admin.ImportResponse;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.curriculum.SectionDto;
import com.wexl.retail.curriculum.service.CurriculumService;
import com.wexl.retail.model.*;
import com.wexl.retail.organization.auth.OrgCurriculumRequest;
import com.wexl.retail.organization.dto.OrganizationResponse;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.organization.repository.OrganizationRepository;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.repository.SectionRepository;
import com.wexl.retail.section.service.SectionService;
import com.wexl.retail.section.service.TeacherSubjectsService;
import com.wexl.retail.student.subject.profiles.domain.StudentSubjectProfile;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfileDetails;
import com.wexl.retail.student.subject.profiles.domain.SubjectProfiles;
import com.wexl.retail.student.subject.profiles.dto.*;
import com.wexl.retail.student.subject.profiles.repository.StudentSubjectProfilesRepository;
import com.wexl.retail.student.subject.profiles.repository.SubjectProfileDetailsRepository;
import com.wexl.retail.student.subject.profiles.repository.SubjectProfilesRepository;
import jakarta.transaction.Transactional;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class SubjectProfilesService {

  private final SubjectProfilesRepository subjectProfilesRepository;
  private final OrganizationRepository organizationRepository;
  private final SubjectProfileDetailsRepository subjectProfileDetailsRepository;
  private final StudentSubjectProfilesRepository studentSubjectProfilesRepository;
  private final UserRepository userRepository;
  private final StudentRepository studentRepository;
  private final SubjectProfileOrgs subjectProfileOrgs;
  private final SectionRepository sectionRepository;
  private final CurriculumService curriculumService;
  private final TeacherSubjectsService teacherSubjectsService;
  private final SectionService sectionService;

  @Transactional
  public void createSubjectProfile(String orgSlug, SubjectProfileRequest subjectProfileRequest) {

    Organization org = organizationRepository.findBySlug(orgSlug);
    SubjectProfiles subjectProfiles = new SubjectProfiles();
    try {
      subjectProfiles.setName(subjectProfileRequest.getName());
      subjectProfiles.setOrg(org);
      subjectProfiles.setStatus(Boolean.TRUE);
      subjectProfiles.setSubjectProfileDetails(
          buildSubjectProfileDetails(subjectProfiles, org, subjectProfileRequest));
      subjectProfilesRepository.save(subjectProfiles);
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubjectsFound.duplication");
    }
  }

  private List<SubjectProfileDetails> buildSubjectProfileDetails(
      SubjectProfiles subjectProfile,
      Organization org,
      SubjectProfileRequest subjectProfileRequest) {
    List<SubjectProfileDetails> subjectProfileDetails = new ArrayList<>();
    List<SubjectsRequest> subjects = subjectProfileRequest.getSubjectsRequest();
    subjects.forEach(
        request ->
            subjectProfileDetails.add(
                SubjectProfileDetails.builder()
                    .org(org)
                    .subjectProfiles(subjectProfile)
                    .boardName(request.getBoardName())
                    .boardSlug(request.getBoardSlug())
                    .gradeSlug(request.getGradeSlug())
                    .gradeName(request.getGradeName())
                    .subjectSlug(request.getSubjectSlug())
                    .subjectName(request.getSubjectName())
                    .displayName(request.getDisplayName())
                    .sourceOrg(organizationRepository.findBySlug(request.getOrgSlug()))
                    .build()));
    return subjectProfileDetails;
  }

  public List<SubjectProfileResponse> getAllSubjectProfilesOfOrg(String orgSlug) {
    Organization org = organizationRepository.findBySlug(orgSlug);

    List<SubjectProfiles> subjectProfiles =
        subjectProfilesRepository.findAllByOrgAndStatusOrderByCreatedAtDesc(org, Boolean.TRUE);

    if (subjectProfiles.isEmpty()) {
      return new ArrayList<>();
    }
    return subjectProfiles.stream()
        .map(s -> SubjectProfileResponse.builder().id(s.getId()).name(s.getName()).build())
        .toList();
  }

  public DetailedSubjectProfileResponse viewSubjectProfile(String orgSlug, Long subjectProfileId) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    SubjectProfiles subjectProfile = getSubjectProfile(org, subjectProfileId);
    List<SubjectProfileDetails> subjectProfileDetails =
        subjectProfileDetailsRepository.findAllBySubjectProfilesAndDeletedAtOrderByCreatedAtDesc(
            subjectProfile, null);

    List<SubjectDetails> subjectDetails = new ArrayList<>();

    subjectProfileDetails.forEach(
        s ->
            subjectDetails.add(
                SubjectDetails.builder()
                    .id(s.getId())
                    .boardName(s.getBoardName())
                    .sourceOrgName(s.getSourceOrg().getName())
                    .gradeName(s.getGradeName())
                    .subjectName(s.getSubjectName())
                    .displayName(s.getDisplayName())
                    .build()));

    return DetailedSubjectProfileResponse.builder()
        .name(subjectProfile.getName())
        .subjectDetails(subjectDetails)
        .build();
  }

  private SubjectProfiles getSubjectProfile(Organization org, Long subjectProfileId) {
    Optional<SubjectProfiles> optionalProfile =
        subjectProfilesRepository.findByOrgAndId(org, subjectProfileId);

    if (optionalProfile.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.SubjectProfileFind.Id",
          new String[] {Long.toString(subjectProfileId)});
    }
    return optionalProfile.get();
  }

  public void addNewSubjectToProfile(
      String orgSlug, SubjectProfileRequest subjectProfileRequest, Long subjectProfileId) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    SubjectProfiles subjectProfile = getSubjectProfile(org, subjectProfileId);
    List<SubjectsRequest> newSubjects = subjectProfileRequest.getSubjectsRequest();
    for (SubjectsRequest newSubject : newSubjects) {
      if (checkIfNewSubjectExistsInSubjectProfile(newSubject, subjectProfile)) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubjectAdd.Profile");
      }

      subjectProfileDetailsRepository.save(
          SubjectProfileDetails.builder()
              .displayName(newSubject.getDisplayName())
              .sourceOrg(organizationRepository.findBySlug(newSubject.getOrgSlug()))
              .subjectName(newSubject.getSubjectName())
              .subjectSlug(newSubject.getSubjectSlug())
              .gradeName(newSubject.getGradeName())
              .gradeSlug(newSubject.getGradeSlug())
              .boardSlug(newSubject.getBoardSlug())
              .boardName(newSubject.getBoardName())
              .subjectProfiles(subjectProfile)
              .org(org)
              .build());
    }
  }

  private boolean checkIfNewSubjectExistsInSubjectProfile(
      SubjectsRequest newSubject, SubjectProfiles subjectProfile) {
    return subjectProfileDetailsRepository
        .findFirstByBoardNameAndAndGradeNameAndSubjectNameAndSubjectProfilesAndSourceOrgAndDeletedAt(
            newSubject.getBoardName(),
            newSubject.getGradeName(),
            newSubject.getSubjectName(),
            subjectProfile,
            organizationRepository.findBySlug(newSubject.getOrgSlug()),
            null)
        .isPresent();
  }

  public void deleteSubjectProfile(String orgSlug, Long subjectProfileId) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    SubjectProfiles subjectProfile = getSubjectProfile(org, subjectProfileId);
    if (checkIfSubjectProfileIsAssociatedWithStudents(subjectProfile)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.student.profile");
    }
    subjectProfile.setDeletedAt(new Date());
    subjectProfile.setUpdatedAt(new Timestamp(new Date().getTime()));
    subjectProfile.setStatus(Boolean.FALSE);
    subjectProfilesRepository.save(subjectProfile);
  }

  private boolean checkIfSubjectProfileIsAssociatedWithStudents(SubjectProfiles subjectProfile) {
    return studentSubjectProfilesRepository
        .findFirstBySubjectProfileAndDeletedAt(subjectProfile, null)
        .isPresent();
  }

  public void deleteASubjectFromProfile(
      String orgSlug, Long subjectProfileId, Long subjectProfileDetailsId) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    SubjectProfiles subjectProfile = getSubjectProfile(org, subjectProfileId);
    List<SubjectProfileDetails> activeSubjects =
        subjectProfile.getSubjectProfileDetails().stream()
            .filter(s -> Objects.isNull(s.getDeletedAt()))
            .toList();
    if (activeSubjects.size() <= 1) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.SubjectDelete.Profile");
    }
    var subjectDetails =
        subjectProfileDetailsRepository
            .findByIdAndDeletedAt(subjectProfileDetailsId, null)
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.NO_RECORD_FOUND,
                        "error.SubjectFind.Profile",
                        new String[] {subjectProfile.getName()}));

    subjectProfileDetailsRepository.deleteSubjectProfileDetails(subjectDetails.getId());
  }

  public void mapSubjectProfileToStudent(String orgSlug, String userName, Long subjectProfileId) {

    Organization org = organizationRepository.findBySlug(orgSlug);

    User studentUser = userRepository.getUserByUserName(userName);

    if (Objects.isNull(studentUser)) {
      return;
    }

    Student student = studentRepository.findByUserId(studentUser.getId());

    SubjectProfiles subjectProfile = getSubjectProfile(org, subjectProfileId);

    if (checkIfAlreadyMappedToStudent(student, subjectProfile)) {
      log.error("This profile is already mapped. Select a different profile");
    }

    studentSubjectProfilesRepository.save(
        StudentSubjectProfile.builder().student(student).subjectProfile(subjectProfile).build());
  }

  private boolean checkIfAlreadyMappedToStudent(Student student, SubjectProfiles subjectProfile) {
    return studentSubjectProfilesRepository
        .findFirstByStudentAndSubjectProfileAndDeletedAt(student, subjectProfile, null)
        .isPresent();
  }

  public List<SubjectProfileResponse> getSubjectProfilesOfStudent(String userName) {
    User studentUser = userRepository.getUserByUserName(userName);

    if (Objects.isNull(studentUser)) {
      return new ArrayList<>();
    }

    Student student = studentUser.getStudentInfo();
    List<SubjectProfiles> subjectProfiles =
        subjectProfilesRepository.getSubjectProfileOfStudent(student.getId());

    if (subjectProfiles.isEmpty()) {
      return new ArrayList<>();
    }

    return subjectProfiles.stream()
        .map(s -> SubjectProfileResponse.builder().id(s.getId()).name(s.getName()).build())
        .toList();
  }

  public List<SubjectProfileDetailsResponse> getSubjectProfileDetailsOfStudent(Long studentId) {

    List<SubjectProfileDetails> subjectProfileDetails =
        subjectProfileDetailsRepository.getSubjectProfileDetailsOfStudent(studentId);

    if (subjectProfileDetails.isEmpty()) {
      return new ArrayList<>();
    }

    return subjectProfileDetails.stream()
        .map(
            s ->
                SubjectProfileDetailsResponse.builder()
                    .id(s.getId())
                    .subjectName(s.getSubjectName())
                    .build())
        .toList();
  }

  public void deleteAProfileOfStudent(String orgSlug, String userName, Long subjectProfileId) {
    Organization org = organizationRepository.findBySlug(orgSlug);

    User studentUser = userRepository.getUserByUserName(userName);

    if (Objects.isNull(studentUser)) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.StudentFind.StudentName",
          new String[] {userName});
    }
    Student student = studentUser.getStudentInfo();
    if (checkIfAtleastOneProfileMappedToStudent(student)) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.StudentDelete.Profile");
    }

    SubjectProfiles subjectProfile = getSubjectProfile(org, subjectProfileId);
    Optional<StudentSubjectProfile> studentSubjectProfile =
        studentSubjectProfilesRepository.findFirstByStudentAndSubjectProfileAndDeletedAt(
            student, subjectProfile, null);
    if (studentSubjectProfile.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.ProfileFind.Student");
    }
    try {
      studentSubjectProfilesRepository.delete(studentSubjectProfile.get());
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.SERVER_ERROR, "error.Delete.Profile");
    }
  }

  private boolean checkIfAtleastOneProfileMappedToStudent(Student student) {
    return studentSubjectProfilesRepository.findAllByStudent(student).size() <= 1;
  }

  public List<OrgCurriculumRequest> buildCurriculumRequest(User user) {

    Optional<Student> student = studentRepository.findByUserInfo(user);

    if (student.isEmpty()) {
      return new ArrayList<>();
    }
    List<SubjectProfileDetails> subjectQueryResults =
        subjectProfileDetailsRepository.getSubjectProfileDetailsForStudent(student.get().getId());
    List<OrgCurriculumRequest> request = new ArrayList<>();
    subjectQueryResults.forEach(
        r ->
            request.add(
                OrgCurriculumRequest.builder()
                    .subjectSlug(r.getSubjectSlug())
                    .boardSlug(r.getBoardSlug())
                    .gradeSlug(r.getGradeSlug())
                    .orgSlug(r.getSourceOrg().getSlug())
                    .subjectDisplayName(r.getDisplayName())
                    .build()));

    return request;
  }

  public List<String> getRelatedOrgsOfStudent(long studentId, String studentOrgSlug) {
    List<SubjectProfileDetails> subjectQueryResults =
        subjectProfileDetailsRepository.getSubjectProfileDetailsForStudent(studentId);
    List<String> profileOrgs =
        subjectQueryResults.stream()
            .map(SubjectProfileDetails::getSourceOrg)
            .map(Organization::getSlug)
            .distinct()
            .collect(Collectors.toList());
    profileOrgs.remove(studentOrgSlug);
    return profileOrgs;
  }

  public List<OrganizationResponse> getSubjectProfileOrgs(String sourceOrgSlug) {

    Organization sourceOrg = organizationRepository.findBySlug(sourceOrgSlug);
    Optional<SubjectProfileOrgs.SubjectProfileOrg> optionalSubjectProfileOrgs =
        subjectProfileOrgs.getConfig().stream()
            .filter(o -> o.getSource().equals(sourceOrgSlug))
            .findFirst();

    if (optionalSubjectProfileOrgs.isEmpty()) {
      return List.of(
          OrganizationResponse.builder().slug(sourceOrgSlug).name(sourceOrg.getName()).build());
    }

    List<String> targetOrgSlugs = optionalSubjectProfileOrgs.get().getTarget();
    List<Organization> targetOrgs = organizationRepository.findAllBySlugIn(targetOrgSlugs);
    List<OrganizationResponse> response =
        new ArrayList<>(
            targetOrgs.stream()
                .map(
                    r -> OrganizationResponse.builder().name(r.getName()).slug(r.getSlug()).build())
                .toList());
    response.add(
        OrganizationResponse.builder().name(sourceOrg.getName()).slug(sourceOrgSlug).build());
    return response;
  }

  @Transactional
  public ImportResponse manageStudentSubjectProfile(
      String orgSlug,
      String username,
      TargetStudentSubjectProfileRequest targetStudentSubjectProfileRequest) {
    Organization org = organizationRepository.findBySlug(orgSlug);
    final List<SubjectProfiles> activeSubjectProfiles =
        subjectProfilesRepository.findAllByOrgAndStatusOrderByCreatedAtDesc(org, true);
    final Student student =
        studentRepository.getStudentByAuthUserIdAndOrgSlug(username, org.getSlug());

    deleteExistingSubjectProfiles(student);
    addSubjectProfileToStudent(
        targetStudentSubjectProfileRequest.getSubjectProfile1(), activeSubjectProfiles, student);
    addSubjectProfileToStudent(
        targetStudentSubjectProfileRequest.getSubjectProfile2(), activeSubjectProfiles, student);
    addSubjectProfileToStudent(
        targetStudentSubjectProfileRequest.getSubjectProfile3(), activeSubjectProfiles, student);
    return new ImportResponse(IMPORT_STATUS_COMPLETED, "Successfully imported");
  }

  private void deleteExistingSubjectProfiles(Student student) {
    final List<StudentSubjectProfile> profilesByStudent =
        studentSubjectProfilesRepository.findAllByStudent(student);
    studentSubjectProfilesRepository.deleteAll(profilesByStudent);
  }

  private void addSubjectProfileToStudent(
      String subjectProfileName, List<SubjectProfiles> activeSubjectProfiles, Student student) {
    if (StringUtils.isBlank(subjectProfileName)) {
      return;
    }
    final Optional<SubjectProfiles> possibleSubjectProfile =
        activeSubjectProfiles.stream()
            .filter(sp -> sp.getName().equalsIgnoreCase(subjectProfileName))
            .findFirst();
    if (possibleSubjectProfile.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "error.SubjectProfile.manageStudents",
          new String[] {subjectProfileName});
    }
    final SubjectProfiles subjectProfile = possibleSubjectProfile.get();
    if (!checkIfAlreadyMappedToStudent(student, subjectProfile)) {
      studentSubjectProfilesRepository.save(
          StudentSubjectProfile.builder().student(student).subjectProfile(subjectProfile).build());
    }
  }

  public void createTeacherSubjectProfile(
      Teacher teacher, String orgSlug, List<EduBoard> curriculumResponse) {

    curriculumResponse.forEach(
        eduBoard ->
            eduBoard
                .getGrades()
                .forEach(
                    grade -> {
                      List<Section> section =
                          sectionRepository.getSectionsOfGrade(orgSlug, grade.getId());
                      List<SectionDto> sectionDtos =
                          sectionService.buildSectionToSectioDto(section);
                      grade.setSections(sectionDtos);
                    }));
    curriculumResponse.forEach(
        board -> {
          List<Grade> grades = board.getGrades();
          grades.forEach(
              grade -> {
                List<Subject> subjects = grade.getSubjects();
                List<SectionDto> sections = grade.getSections();
                subjects.forEach(
                    subject ->
                        sections.forEach(
                            sectionDto ->
                                teacherSubjectsService.save(
                                    orgSlug,
                                    board.getSlug(),
                                    teacher.getUserInfo().getAuthUserId(),
                                    sectionDto.getUuid().toString(),
                                    Collections.singletonList(subject.getSlug()),
                                    false)));
              });
        });
  }
}
