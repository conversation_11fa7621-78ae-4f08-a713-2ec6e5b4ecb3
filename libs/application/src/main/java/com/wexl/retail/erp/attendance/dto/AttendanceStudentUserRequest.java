package com.wexl.retail.erp.attendance.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AttendanceStudentUserRequest {
  @JsonProperty("timePeriod")
  Integer timePeriod;

  @JsonProperty("key")
  String key;

  @JsonProperty("teacher_id")
  Long teacherId;
}
