package com.wexl.retail.subjects.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "subject_metadata")
public class SubjectsMetaData extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  @Column(name = "category")
  @Enumerated(EnumType.STRING)
  private SubjectsCategoryEnum categoryEnum;

  @Column(name = "type")
  @Enumerated(EnumType.STRING)
  private SubjectsTypeEnum SubjectsTypeEnum;

  @Column(name = "board_slug")
  private String boardSlug;

  @Column(name = "grade_slug")
  private String gradeSlug;

  @Column(name = "seq_no")
  private Long seqNo;

  @Column(name = "org_slug")
  private String orgSlug;

  @Column(name = "wexl_subject_slug")
  private String wexlSubjectSlug;

  private Boolean status;

  @OneToMany(cascade = CascadeType.ALL, mappedBy = "subjectsMetaData", fetch = FetchType.LAZY)
  private List<SubjectsMetadataStudents> subjectsMetadataStudents;
}
