package com.wexl.retail.auth;

import com.wexl.retail.model.User;

public class AuthUtil {

  private AuthUtil() {}

  public static boolean isTeacher(User user) {
    return UserRoleHelper.get().isTeacher(user);
  }

  public static boolean isStudent(User user) {
    return UserRoleHelper.get().isStudent(user);
  }

  public static boolean isOrgAdmin(User user) {
    return UserRoleHelper.get().isOrgAdmin(user);
  }

  public static boolean isStaff(User user) {
    return UserRoleHelper.get().isStaff(user);
  }
}
