package com.wexl.retail.test.school.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.wexl.retail.content.model.QuestionType;
import java.util.List;
import lombok.Builder;

@Builder
public record PbqDto() {

  @Builder
  public record Data(@JsonProperty("answers") List<Answers> answers) {}

  @Builder
  public record Answers(QuestionType type, Mcq mcq, YesNo yesNo, Msq msq, Nat nat) {}

  @Builder
  public record Mcq(
      @JsonProperty("question_uuid") String questionUuid,
      Integer answer,
      @JsonProperty("selected_answer_text") String answerText,
      @JsonProperty("selected_answer") Integer selectedAnswer) {}

  public record YesNo(@JsonProperty("question_uuid") String questionUuid, Boolean answer) {}

  public record Nat(@JsonProperty("question_uuid") String questionUuid, Float answer) {}

  public record Msq(
      @JsonProperty("question_uuid") String questionUuid,
      List<Long> answer,
      List<Long> selectedAnswer) {}
}
