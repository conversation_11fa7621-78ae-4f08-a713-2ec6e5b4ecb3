package com.wexl.retail.maths.model;

import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Entity
@AllArgsConstructor
@RequiredArgsConstructor
@Data
@Table(name = "ccss_standards")
public class CcssStandard extends Model {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  private String slug;

  @ManyToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "ccss_cluster_id")
  private CcssCluster ccssCluster;
}
