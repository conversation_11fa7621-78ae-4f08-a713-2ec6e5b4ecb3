package com.wexl.retail.model;

import com.wexl.retail.globalprofile.model.RoleTemplate;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import io.hypersistence.utils.hibernate.type.json.JsonType;
import jakarta.persistence.*;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.WhereJoinTable;

@Getter
@Setter
@Entity
@Table(name = "teacher_details")
public class Teacher extends Model {
  @Id
  @GeneratedValue(
      strategy = GenerationType.SEQUENCE,
      generator = "teacher-details-sequence-generator")
  @SequenceGenerator(
      name = "teacher-details-sequence-generator",
      sequenceName = "teacher_details_seq",
      allocationSize = 1)
  private long id;

  private String instituteName;
  private String panNumber;
  private String businessType;
  private int numberOfStudents;
  private String referenceFrom;
  private String teacherCode;
  private String bankDetails;
  private String educationalDetails;
  private String teachingDetails;

  @OneToOne(fetch = FetchType.LAZY)
  @JoinColumn(name = "userId")
  private User userInfo;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(
      name = "teacher_sections",
      joinColumns = @JoinColumn(name = "teacher_id"),
      inverseJoinColumns = @JoinColumn(name = "section_id"))
  @WhereJoinTable(clause = "deleted_at IS NULL")
  private Set<Section> sections;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(
      name = "teacher_orgs",
      joinColumns = @JoinColumn(name = "teacher_id"),
      inverseJoinColumns = @JoinColumn(name = "child_org"))
  @WhereJoinTable(clause = "deleted_at IS NULL")
  private List<Organization> childOrgs;

  public Set<Section> getSections() {
    if (Objects.isNull(sections)) {
      return new HashSet<>();
    }
    return sections;
  }

  @Type(JsonType.class)
  @Column(columnDefinition = "jsonb", nullable = false)
  private TeacherMetadata metadata;

  @OneToOne(cascade = CascadeType.ALL)
  private RoleTemplate roleTemplate;
}
