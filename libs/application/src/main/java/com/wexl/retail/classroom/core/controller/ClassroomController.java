package com.wexl.retail.classroom.core.controller;

import com.wexl.retail.classroom.core.dto.ClassroomDetails;
import com.wexl.retail.classroom.core.dto.ClassroomRequest;
import com.wexl.retail.classroom.core.dto.ClassroomResponse;
import com.wexl.retail.classroom.core.dto.TaskResponse;
import com.wexl.retail.classroom.core.service.ClassroomService;
import com.wexl.retail.commons.security.annotation.IsOrgAdmin;
import com.wexl.retail.commons.security.annotation.IsOrgAdminOrTeacher;
import com.wexl.retail.metrics.classroom.ClassroomAttendanceService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/orgs/{orgSlug}/classrooms")
public class ClassroomController {
  private final ClassroomService classroomService;

  private final ClassroomAttendanceService classroomAttendanceService;

  @IsOrgAdmin
  @PostMapping
  @ResponseStatus(HttpStatus.CREATED)
  public void createClassroom(
      @PathVariable String orgSlug, @RequestBody ClassroomRequest classRoomRequest) {
    classroomService.createClassroom(classRoomRequest, orgSlug);
  }

  @IsOrgAdmin
  @PostMapping("/{classroomId}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void editClassroom(
      @PathVariable String orgSlug,
      @PathVariable long classroomId,
      @RequestBody ClassroomRequest classRoomRequest) {

    classroomService.editClassroom(classRoomRequest, orgSlug, classroomId);
  }

  @IsOrgAdmin
  @DeleteMapping("/{classroomId}")
  @ResponseStatus(HttpStatus.ACCEPTED)
  public void deleteClassroom(@PathVariable String orgSlug, @PathVariable long classroomId) {

    classroomService.deleteClassroom(orgSlug, classroomId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping
  public List<ClassroomResponse> getAllClassRooms(
      @PathVariable String orgSlug,
      @RequestParam(required = false, defaultValue = "false") boolean isClassroomReport,
      @RequestParam(required = false, defaultValue = "false") boolean isToday) {

    return classroomService.getAllClassRoomResponses(orgSlug, isClassroomReport, isToday);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/{classroomId}")
  public ClassroomDetails getClassRoomDetails(
      @PathVariable String orgSlug, @PathVariable Long classroomId) {

    return classroomService.getAllClassRoomDetails(orgSlug, classroomId);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/{classroomId}/tasks")
  public TaskResponse getScheduleInstByClassroom(
      @PathVariable String orgSlug,
      @PathVariable Long classroomId,
      @RequestParam(value = "from_date", required = false) Long fromDate,
      @RequestParam(value = "to_date", required = false) Long toDate,
      @RequestParam(required = false, defaultValue = "100") int limit) {

    return classroomService.getTaskResponsesByClassroomId(
        orgSlug, classroomId, fromDate, toDate, limit);
  }

  @IsOrgAdminOrTeacher
  @GetMapping("/notification:absentees")
  public ResponseEntity<Void> sendNotificationToClassAbsentees(
      @PathVariable String orgSlug,
      @RequestParam(required = false, defaultValue = "false") boolean isLongAbsentees) {
    classroomAttendanceService.sendNotificationToClassAbsentees(orgSlug, isLongAbsentees);
    return ResponseEntity.ok().build();
  }
}
