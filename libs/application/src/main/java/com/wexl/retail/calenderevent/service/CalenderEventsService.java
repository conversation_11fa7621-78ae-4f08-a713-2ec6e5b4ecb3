package com.wexl.retail.calenderevent.service;

import com.wexl.retail.auth.AuthService;
import com.wexl.retail.calenderevent.dto.CalenderEventDto;
import com.wexl.retail.calenderevent.dto.CalenderEventDto.LessonPlannerRequest;
import com.wexl.retail.calenderevent.dto.CalenderEventQueryResult;
import com.wexl.retail.calenderevent.dto.CalenderEventType;
import com.wexl.retail.calenderevent.dto.CalenderEventVisibility;
import com.wexl.retail.calenderevent.model.CalendarEvent;
import com.wexl.retail.calenderevent.model.CalendarEventUser;
import com.wexl.retail.calenderevent.repository.CalendarEventUserRepository;
import com.wexl.retail.calenderevent.repository.CalenderEventRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.communications.circulars.service.CommunicationFeature;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.content.model.Grade;
import com.wexl.retail.content.model.SubTopicResponse;
import com.wexl.retail.curriculum.service.OrgSettingsService;
import com.wexl.retail.globalprofile.model.AppTemplate;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.model.User;
import com.wexl.retail.notification.service.EventNotificationService;
import com.wexl.retail.notifications.dto.NotificationDto;
import com.wexl.retail.notifications.dto.NotificationType;
import com.wexl.retail.notifications.model.Notification;
import com.wexl.retail.notifications.repository.NotificationRepository;
import com.wexl.retail.notifications.service.NotificationsService;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.TeacherRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.section.domain.TeacherSection;
import com.wexl.retail.section.repository.TeacherSectionRepository;
import com.wexl.retail.util.StrapiService;
import com.wexl.retail.util.ValidationUtils;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CalenderEventsService {

  @Value("${app.latestAcademicYear}")
  private String latestAcademicYear;

  @Value("${app.contentToken}")
  private String contentBearerToken;

  private final OrgSettingsService orgSettingsService;
  private final ContentService contentService;
  private final DateTimeUtil dateTimeUtil;
  private final CalenderEventRepository calenderEventRepository;
  private final CalendarEventUserRepository calendarEventUserRepository;
  private final ValidationUtils validationUtils;
  private final StudentRepository studentRepository;
  private final TeacherSectionRepository teacherSectionRepository;
  private final StrapiService strapiService;
  private final AuthService authService;
  private final UserRepository userRepository;
  private final NotificationsService notificationsService;
  private final EventNotificationService eventNotificationService;
  private final TeacherRepository teacherRepository;
  private final NotificationRepository notificationRepository;

  public void saveLessonPlanner(String orgSlug, CalenderEventDto.LessonPlannerRequest request) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    calenderEventRepository.save(buildLessonPlanner(orgSlug, request, new CalendarEvent()));
  }

  private CalendarEvent buildLessonPlanner(
      String orgSlug, CalenderEventDto.LessonPlannerRequest request, CalendarEvent calendarEvent) {
    calendarEvent.setTitle(request.title());
    calendarEvent.setType(request.type());
    calendarEvent.setBoardSlug(request.boardSlug());
    calendarEvent.setDescription(request.description());
    calendarEvent.setGradeSlug(request.gradeSlug());
    calendarEvent.setAssets(getAssets(request));
    calendarEvent.setColour(request.colour());
    calendarEvent.setAcademicYearSlug(latestAcademicYear);
    calendarEvent.setVisibility(request.visibility());
    calendarEvent.setDate(dateTimeUtil.convertEpochToTimestamp(request.date()).toLocalDateTime());
    calendarEvent.setSectionUuid(request.sectionUuid());
    calendarEvent.setCalendarEventUsers(buildCalenderEventUsers(request, calendarEvent));
    calendarEvent.setOrgSlug(orgSlug);
    return calendarEvent;
  }

  private List<String> getAssets(LessonPlannerRequest request) {
    if (request.assetSlugs() == null || request.assetSlugs().isEmpty()) {
      return new ArrayList<>();
    }
    return request.assetSlugs();
  }

  private List<CalendarEventUser> buildCalenderEventUsers(
      CalenderEventDto.LessonPlannerRequest request, CalendarEvent calendarEvent) {
    List<CalendarEventUser> calendarEventUserList = new ArrayList<>();
    if (CalenderEventVisibility.ALL.equals(request.visibility())) {
      var section = validationUtils.findSectionByUuid(request.sectionUuid());
      calendarEventUserList.addAll(buildStudentData(section, calendarEvent));
      calendarEventUserList.addAll(buildTeacherData(section, calendarEvent));
    } else {
      List<Long> studentIds = getIdsFromList(request.studentsList());
      List<Long> teacherIds = getIdsFromList(request.teachersList());
      calendarEventUserList.addAll(
          buildUsers(studentIds, Boolean.TRUE, Boolean.FALSE, calendarEvent));
      calendarEventUserList.addAll(
          buildUsers(teacherIds, Boolean.FALSE, Boolean.TRUE, calendarEvent));
    }

    return calendarEventUserList;
  }

  private List<Long> getIdsFromList(List<Long> list) {
    return list != null ? new ArrayList<>(list) : null;
  }

  private List<CalendarEventUser> buildTeacherData(Section section, CalendarEvent calendarEvent) {
    List<CalendarEventUser> calendarEventUserList = new ArrayList<>();
    var teacherSectionList = teacherSectionRepository.findAllBySection(section);
    var teacherUserId =
        teacherSectionList.stream().map(TeacherSection::getTeacher).toList().stream()
            .map(Teacher::getUserInfo)
            .toList()
            .stream()
            .map(User::getId)
            .toList();
    calendarEventUserList.addAll(
        buildUsers(teacherUserId, Boolean.FALSE, Boolean.TRUE, calendarEvent));
    return calendarEventUserList;
  }

  private List<CalendarEventUser> buildStudentData(Section section, CalendarEvent calendarEvent) {
    List<CalendarEventUser> calendarEventUserList = new ArrayList<>();
    var students = studentRepository.getStudentsBySectionAndDeletedAtIsNull(section);
    var studentIds =
        students.stream().map(Student::getUserInfo).toList().stream().map(User::getId).toList();
    calendarEventUserList.addAll(
        buildUsers(studentIds, Boolean.TRUE, Boolean.FALSE, calendarEvent));
    return calendarEventUserList;
  }

  private List<CalendarEventUser> buildUsers(
      List<Long> userIds, boolean isStudent, boolean isTeacher, CalendarEvent calendarEvent) {
    List<CalendarEventUser> calendarEventUserList = new ArrayList<>();

    if (userIds != null) {
      userIds.forEach(
          id -> {
            CalendarEventUser calendarEventUser =
                CalendarEventUser.builder()
                    .userId(id)
                    .isStudent(isStudent)
                    .isTeacher(isTeacher)
                    .calendarEvent(calendarEvent)
                    .build();
            calendarEventUserList.add(calendarEventUser);
          });
    }

    return calendarEventUserList;
  }

  public void saveEvent(String orgSlug, CalenderEventDto.EventRequest request) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    NotificationDto.NotificationRequest notificationRequest =
        NotificationDto.NotificationRequest.builder()
            .title(request.title())
            .message(request.description())
            .notificationType(NotificationType.ORGANIZATION)
            .feature(CommunicationFeature.CALENDAR_EVENT)
            .userAuthId(request.authUserId())
            .build();
    Long notificationId =
        notificationsService.createNotificationForOrganization(
            orgSlug,
            notificationRequest,
            request.authUserId() == null
                ? authService.getTeacherDetails().getAuthUserId()
                : request.authUserId(),
            true,
            null);
    eventNotificationService.sendPushNotificationForOrganization(
        orgSlug, request.title(), request.description());
    CalendarEvent calendarEvent =
        CalendarEvent.builder()
            .colour(request.colour())
            .date(dateTimeUtil.convertEpochToTimestamp(request.date()).toLocalDateTime())
            .description(request.description())
            .title(request.title())
            .type(request.type())
            .visibility(CalenderEventVisibility.ALL)
            .academicYearSlug(latestAcademicYear)
            .orgSlug(orgSlug)
            .notificationId(notificationId)
            .build();
    calenderEventRepository.save(calendarEvent);
  }

  public List<CalenderEventDto.Response> getCalenderDataByDate(
      String orgSlug,
      Long startDateEpoch,
      Long endDateEpoch,
      CalenderEventType type,
      List<String> grade,
      List<String> board,
      List<String> section) {
    orgSettingsService.validateOrganizaiton(orgSlug);
    var startDate = dateTimeUtil.convertEpochToIso8601(startDateEpoch);
    var endDate = dateTimeUtil.convertEpochToIso8601(endDateEpoch);
    List<CalendarEvent> dataList;
    if (type == null) {
      dataList =
          calenderEventRepository.findAllByOrgSlugAndAcademicYearSlugAndDateBetween(
              orgSlug,
              latestAcademicYear,
              startDate.with(LocalTime.MIN),
              endDate.with(LocalTime.MAX).minusSeconds(1));
    } else if (type.equals(CalenderEventType.EVENT)) {
      dataList =
          calenderEventRepository.findAllByOrgSlugAndAcademicYearSlugAndTypeAndDateBetween(
              orgSlug, latestAcademicYear, type, startDate, endDate);
    } else {
      dataList =
          calenderEventRepository.getAllLessonPlannerDataByDate(
              orgSlug, board, grade, section, latestAcademicYear, startDate, endDate);
    }
    return buildCalenderDataByDateResponse(dataList);
  }

  private List<CalenderEventDto.Response> buildCalenderDataByDateResponse(
      List<CalendarEvent> dataList) {
    List<CalenderEventDto.Response> responseList = new ArrayList<>();
    var gradeEntity = strapiService.getAllGrades();
    dataList.forEach(
        data -> {
          if (data.getType().equals(CalenderEventType.LESSON_PLANNER)) {
            responseList.add(buildLessonPlannerResponse(gradeEntity, data));

          } else {
            responseList.add(buildEventResponse(data));
          }
        });
    return responseList;
  }

  private CalenderEventDto.Response buildEventResponse(CalendarEvent d) {
    return CalenderEventDto.Response.builder()
        .id(d.getId())
        .date(DateTimeUtil.convertIso8601ToEpoch(d.getDate()))
        .colour(d.getColour())
        .type(d.getType())
        .description(d.getDescription())
        .title(d.getTitle())
        .visibility(d.getVisibility())
        .build();
  }

  private CalenderEventDto.Response buildLessonPlannerResponse(
      List<Grade> gradeEntity, CalendarEvent d) {
    var grade = validationUtils.findGradeBySlug(gradeEntity, d.getGradeSlug());
    var section = validationUtils.findSectionByUuid(d.getSectionUuid());
    var calenderEventUserIds =
        d.getCalendarEventUsers().stream().map(CalendarEventUser::getUserId).toList();
    if (d.getAssets().isEmpty()) {
      return getResponseForEmptyAssets(d, grade, section, calenderEventUserIds);
    }
    var assetResponse = contentService.getAssetBySlug(d.getAssets().getFirst());
    var subtopicResponse =
        contentService.getSubTopicBySlug(
            d.getOrgSlug(), assetResponse.subtopicSlug, contentBearerToken);
    return getResponse(d, grade, section, subtopicResponse, calenderEventUserIds);
  }

  private CalenderEventDto.Response getResponseForEmptyAssets(
      CalendarEvent d, Grade grade, Section section, List<Long> calenderEventUserIds) {
    return CalenderEventDto.Response.builder()
        .id(d.getId())
        .date(DateTimeUtil.convertIso8601ToEpoch(d.getDate()))
        .colour(d.getColour())
        .type(d.getType())
        .description(d.getDescription())
        .title(d.getTitle())
        .assets(contentService.getAssetLinks(d.getAssets()))
        .visibility(d.getVisibility())
        .boardSlug(section.getBoardSlug())
        .boardName(section.getBoardName())
        .sectionName(section.getName())
        .sectionUuid(section.getUuid().toString())
        .gradeName(grade.getName())
        .gradeSlug(grade.getSlug())
        .calenderEventUserIds(calenderEventUserIds)
        .build();
  }

  private CalenderEventDto.Response getResponse(
      CalendarEvent d,
      Grade grade,
      Section section,
      SubTopicResponse subtopicResponse,
      List<Long> calenderEventUserIds) {
    return CalenderEventDto.Response.builder()
        .id(d.getId())
        .date(DateTimeUtil.convertIso8601ToEpoch(d.getDate()))
        .colour(d.getColour())
        .type(d.getType())
        .description(d.getDescription())
        .title(d.getTitle())
        .assets(contentService.getAssetLinks(d.getAssets()))
        .visibility(d.getVisibility())
        .boardSlug(section.getBoardSlug())
        .boardName(section.getBoardName())
        .subjectSlug(subtopicResponse.getSubjectSlug())
        .subjectName(subtopicResponse.getSubjectName())
        .chapterName(subtopicResponse.getChapterName())
        .chapterSlug(subtopicResponse.getChapterSlug())
        .sectionName(section.getName())
        .sectionUuid(section.getUuid().toString())
        .gradeName(grade.getName())
        .gradeSlug(grade.getSlug())
        .calenderEventUserIds(calenderEventUserIds)
        .build();
  }

  public List<CalenderEventDto.Response> getTeacherCalenderData(
      String orgSlug, Long startDateEpoch, Long endDateEpoch, String teacherAuthId) {
    User user = authService.getUserByAuthUserId(teacherAuthId);
    var fromDate = dateTimeUtil.convertEpochToIso8601(startDateEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601(endDateEpoch);
    var data =
        calendarEventUserRepository.getCalenderDataByUserId(
            orgSlug, latestAcademicYear, fromDate, toDate, user.getId());
    return buildCalendarDataResponse(data);
  }

  public List<CalenderEventDto.Response> getStudentCalenderData(
      String orgSlug, Long startDateEpoch, Long endDateEpoch, String studentAuthId) {
    User user = authService.getUserByAuthUserId(studentAuthId);

    var fromDate = dateTimeUtil.convertEpochToIso8601(startDateEpoch);
    var toDate = dateTimeUtil.convertEpochToIso8601(endDateEpoch);
    var data =
        calendarEventUserRepository.getCalenderDataByUserId(
            orgSlug,
            latestAcademicYear,
            fromDate.with(LocalTime.MIN),
            toDate.with(LocalTime.MAX),
            user.getId());
    return buildCalendarDataResponse(data);
  }

  private List<CalenderEventDto.Response> buildCalendarDataResponse(
      List<CalenderEventQueryResult> dataList) {
    List<CalenderEventDto.Response> responseList = new ArrayList<>();
    var gradeEntity = strapiService.getAllGrades();

    dataList.forEach(
        data -> {
          if (data.getType().equals(CalenderEventType.LESSON_PLANNER.name())) {
            responseList.add(buildUserLessonPlannerResponse(data, gradeEntity));
          } else {
            responseList.add(buildUserEventResponse(data));
          }
        });
    return responseList.stream()
        .sorted(Comparator.comparing(CalenderEventDto.Response::date).reversed())
        .toList();
  }

  private CalenderEventDto.Response buildUserEventResponse(CalenderEventQueryResult data) {
    return CalenderEventDto.Response.builder()
        .id(data.getId())
        .date(DateTimeUtil.convertIso8601ToEpoch(data.getDueDate()))
        .colour(data.getColour())
        .type(CalenderEventType.EVENT)
        .description(data.getDescription())
        .title(data.getTitle())
        .visibility(CalenderEventVisibility.ALL)
        .build();
  }

  private CalenderEventDto.Response buildUserLessonPlannerResponse(
      CalenderEventQueryResult d, List<Grade> gradeEntity) {
    var grade = validationUtils.findGradeBySlug(gradeEntity, d.getGrade());
    var section = validationUtils.findSectionByUuid(d.getSectionUuid());

    return CalenderEventDto.Response.builder()
        .id(d.getId())
        .date(DateTimeUtil.convertIso8601ToEpoch(d.getDueDate()))
        .colour(d.getColour())
        .type(CalenderEventType.LESSON_PLANNER)
        .description(d.getDescription())
        .assets(contentService.getAssetLinks(d.getAssetSlugs()))
        .title(d.getTitle())
        .visibility(CalenderEventVisibility.valueOf(d.getVisibility()))
        .sectionName(section.getName())
        .sectionUuid(section.getUuid().toString())
        .gradeName(grade.getName())
        .gradeSlug(grade.getSlug())
        .build();
  }

  public void deleteCalenderData(Long calenderEventId) {
    CalendarEvent calenderEvent = validateCalenderEventId(calenderEventId);
    notificationsService.deleteNotification(
        calenderEvent.getOrgSlug(),
        authService.getUserDetails().getAuthUserId(),
        calenderEvent.getNotificationId());
    calenderEventRepository.deleteById(calenderEventId);
  }

  private CalendarEvent validateCalenderEventId(Long calenderEventId) {
    var optionalCalenderEvent = calenderEventRepository.findById(calenderEventId);
    if (optionalCalenderEvent.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Invalid CalenderEvent Id: [" + calenderEventId + "]");
    }
    return optionalCalenderEvent.get();
  }

  public List<CalenderEventDto.CalenderResponse> getCalenderDatesByUser(
      String orgSlug, Long fDateEpoc, Long tDateEpoc) {
    LocalDateTime fDate;
    fDate = dateTimeUtil.convertEpochToIso8601Legacy(fDateEpoc);
    LocalDateTime tDate;
    tDate = dateTimeUtil.convertEpochToIso8601Legacy(tDateEpoc);
    User user;
    user = authService.getUserDetails();
    var userData = validationUtils.isValidUser(user.getAuthUserId());
    var teacherData = userData.getTeacherInfo();
    if (teacherData != null && teacherData.getRoleTemplate() == null) {
      return new ArrayList<>();
    } else if (teacherData != null
        && teacherData.getRoleTemplate().getTemplate().equals(AppTemplate.ADMIN)) {
      return buildCalenderResponse(
          calendarEventUserRepository.getCalenderDataByUserId(
              orgSlug, latestAcademicYear, fDate, tDate, null));
    }

    return buildCalenderResponse(
        calendarEventUserRepository.getCalenderDataByUserId(
            orgSlug, latestAcademicYear, fDate, tDate, user.getId()));
  }

  private List<CalenderEventDto.CalenderResponse> buildCalenderResponse(
      List<CalenderEventQueryResult> dataList) {
    Map<LocalDate, List<CalenderEventQueryResult>> eventsByDate;
    eventsByDate = groupEventsByDate(dataList);
    return createCalenderResponses(eventsByDate);
  }

  private Map<LocalDate, List<CalenderEventQueryResult>> groupEventsByDate(
      List<CalenderEventQueryResult> dataList) {
    return dataList.stream()
        .collect(Collectors.groupingBy(result -> result.getDueDate().toLocalDate()));
  }

  private List<CalenderEventDto.CalenderResponse> createCalenderResponses(
      Map<LocalDate, List<CalenderEventQueryResult>> eventsByDate) {
    return eventsByDate.entrySet().stream()
        .map(
            entry -> {
              LocalDate date = entry.getKey();
              List<CalenderEventQueryResult> eventList = entry.getValue();
              List<CalenderEventDto.Data> responseDataList = new ArrayList<>();
              for (CalenderEventQueryResult event : eventList) {
                CalenderEventDto.Data data =
                    CalenderEventDto.Data.builder()
                        .type(event.getType())
                        .colour(event.getColour())
                        .build();
                responseDataList.add(data);
              }

              return CalenderEventDto.CalenderResponse.builder()
                  .date(DateTimeUtil.convertIso8601ToEpoch(date.atStartOfDay()))
                  .data(responseDataList)
                  .build();
            })
        .toList();
  }

  public List<CalenderEventDto.CalenderUserResponse> getCalenderUserData(Long calenderEventId) {
    var calenderEvent = validateCalenderEventId(calenderEventId);
    List<CalenderEventDto.CalenderUserResponse> responseList = new ArrayList<>();
    var userData = calenderEvent.getCalendarEventUsers();

    for (var user : userData) {
      var userOptional = userRepository.findById(user.getUserId());
      if (userOptional.isEmpty()) {
        throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "User Not found");
      }
      var userDetails = userOptional.get();
      var userResponseBuilder =
          CalenderEventDto.CalenderUserResponse.builder()
              .name(userDetails.getFirstName() + " " + userDetails.getLastName())
              .isStudent(user.getIsStudent())
              .authUserId(userDetails.getAuthUserId())
              .isTeacher(user.getIsTeacher());
      responseList.add(userResponseBuilder.build());
    }

    return responseList;
  }

  public void updateLessonPlanner(String orgSlug, long lessonPlanId, LessonPlannerRequest request) {
    var calenderEvent = validateCalenderEventId(lessonPlanId);
    calenderEventRepository.save(buildLessonPlanner(orgSlug, request, calenderEvent));
    Optional<Notification> newNotificationRequest =
        notificationRepository.findById(calenderEvent.getNotificationId());
    if (newNotificationRequest.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Notification Not found");
    }
    NotificationDto.NotificationRequest notificationRequest = notificationRequestBuilder(request);
    notificationsService.editNotificationByTeacher(
        orgSlug,
        notificationRequest,
        notificationRequest.userAuthId(),
        calenderEvent.getNotificationId());
  }

  public CalenderEventDto.StudentCommunicationResponse getStudentCalendarActivities(
      String orgSlug, String studentAuthId, Long fromDate, Long toDate) {
    var communicationReponses =
        notificationsService.getNotificationsByFeatures(
            orgSlug, studentAuthId, fromDate, toDate, List.of(CommunicationFeature.values()));
    return CalenderEventDto.StudentCommunicationResponse.builder()
        .communicationNotifications(communicationReponses)
        .build();
  }

  public CalenderEventDto.StudentCommunicationResponse getTeacherCalendarActivities(
      String orgSlug, String teacherAuthId, Long fromDate, Long toDate) {
    var communicationResponses =
        notificationsService.getTeacherNotificationsByFeatures(
            orgSlug, teacherAuthId, fromDate, toDate, List.of(CommunicationFeature.values()));
    return CalenderEventDto.StudentCommunicationResponse.builder()
        .communicationNotifications(communicationResponses)
        .build();
  }

  public NotificationDto.NotificationRequest notificationRequestBuilder(
      CalenderEventDto.LessonPlannerRequest request) {
    return NotificationDto.NotificationRequest.builder()
        .title(request.title())
        .message(request.description())
        .notificationType(NotificationType.ORGANIZATION)
        .feature(CommunicationFeature.CALENDAR_EVENT)
        .userAuthId(authService.getUserDetails().getAuthUserId())
        .build();
  }
}
