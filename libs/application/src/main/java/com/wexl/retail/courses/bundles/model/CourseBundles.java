package com.wexl.retail.courses.bundles.model;

import com.wexl.retail.courses.definition.model.CourseDefinition;
import com.wexl.retail.model.Model;
import jakarta.persistence.*;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;

@Data
@Builder
@RequiredArgsConstructor
@Entity
@AllArgsConstructor
@Table(name = "course_bundles")
public class CourseBundles extends Model {
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  private String name;

  @ManyToMany(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
  @JoinTable(
      name = "course_definition_bundles",
      joinColumns = @JoinColumn(name = "coursebundles_id"),
      inverseJoinColumns = @JoinColumn(name = "course_definition_id"))
  List<CourseDefinition> courseDefinitions;

  private String orgSlug;

  @Column(name = "thumb_nail")
  private String thumbnail;
}
