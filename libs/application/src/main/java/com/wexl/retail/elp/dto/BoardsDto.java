package com.wexl.retail.elp.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record BoardsDto() {

  @Builder
  public record Boards(
      @JsonProperty("name") String boardName,
      @JsonProperty("slug") String boardSlug,
      Long boardId,
      List<Grades> grades) {}

  @Builder
  public record Grades(Long gradeId, String gradeName, String gradeSlug, List<Subjects> subjects) {}

  @Builder
  public record Subjects(String subjectName, String subjectSlug, List<Chapters> chapters) {}

  @Builder
  public record Chapters(
      Long chapterId, String chapterName, String chapterSlug, List<SubTopics> subTopics) {}

  @Builder
  public record SubTopics(Long subtopicId, String subtopicName, String subtopicSlug) {}
}
