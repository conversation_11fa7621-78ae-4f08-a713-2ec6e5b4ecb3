package com.wexl.retail.erp.attendance.dto;

import com.wexl.retail.erp.attendance.domain.CalenderDetails;
import com.wexl.retail.erp.attendance.domain.CompletionStatus;
import com.wexl.retail.model.Teacher;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SectionAttendanceRequest {
  private Section section;
  private Boolean isCompleted;
  private Organization org;
  private Integer dateId;
  private Teacher teacher;
  private CalenderDetails calender;
  private CompletionStatus attendanceStatus;
}
