package com.wexl.retail.whatsapp;

import com.wexl.retail.model.StudentAudit;
import com.wexl.retail.notifications.model.NotificationType;
import com.wexl.retail.repository.StudentAuditRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.whatsapp.interakt.dto.Request;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AuditService {

  private final UserRepository userRepository;
  private final StudentAuditRepository studentAuditRepository;
  private final UserService userService;

  @Async
  public void saveAudit(
      List<Request.Recipient> recipients,
      String teacherAuthId,
      String remark,
      NotificationType notificationType,
      String templateId) {

    var teacherDetails =
        teacherAuthId == null ? null : userRepository.findByAuthUserId(teacherAuthId);
    List<StudentAudit> studentAudits = new ArrayList<>();

    for (Request.Recipient recipient : recipients) {
      StudentAudit studentAudit = new StudentAudit();
      studentAudit.setStudentId(recipient.studentId() != null ? recipient.studentId() : 0L);
      studentAudit.setTeacherId(teacherDetails != null ? teacherDetails.get().getId() : 0L);
      studentAudit.setTeacherName(
          teacherDetails != null ? userService.getNameByUserInfo(teacherDetails.get()) : null);
      studentAudit.setAction(remark);
      studentAudit.setUserId(recipient.userId() != null ? recipient.userId() : 0L);
      studentAudit.setMobileNumber(recipient.mobileNumber().substring(3));
      studentAudit.setStatus(remark);
      studentAudit.setType(notificationType);
      studentAudit.setTemplateId(templateId);
      studentAudits.add(studentAudit);
    }
    studentAuditRepository.saveAll(studentAudits);
  }
}
