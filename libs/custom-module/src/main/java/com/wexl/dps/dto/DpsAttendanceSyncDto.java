package com.wexl.dps.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Builder;

public record DpsAttendanceSyncDto() {
  @Builder
  public record AttendanceAndRemarksRequest(
      @JsonProperty("total_attendance") Long totalAttendance,
      @JsonProperty("attendance") List<Attendance> attendance) {}

  @Builder
  public record Attendance(
      @JsonProperty("student_code") String studentCode,
      @JsonProperty("total_present_days") Long presentDays) {}
}
