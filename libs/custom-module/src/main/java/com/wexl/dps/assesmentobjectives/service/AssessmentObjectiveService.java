package com.wexl.dps.assesmentobjectives.service;

import com.wexl.dps.assesmentobjectives.dto.AssessmentObjectiveDto;
import com.wexl.dps.assesmentobjectives.model.*;
import com.wexl.dps.assesmentobjectives.repository.*;
import com.wexl.dps.preprimary.service.PrePrimaryAprService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import com.wexl.retail.content.ContentService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.Subject;
import com.wexl.retail.reportcards.dto.ReportCardConfigDto;
import com.wexl.retail.repository.StudentRepository;
import com.wexl.retail.repository.UserRepository;
import com.wexl.retail.subjects.model.SubjectsMetaData;
import com.wexl.retail.subjects.model.SubjectsMetadataStudents;
import com.wexl.retail.subjects.repository.SubjectsMetadataStudentsRepository;
import com.wexl.retail.term.model.Term;
import com.wexl.retail.term.repository.TermRepository;
import com.wexl.retail.term.service.TermService;
import com.wexl.retail.util.StrapiService;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class AssessmentObjectiveService {

  private final AssessmentObjectiveRepository assessmentObjectiveRepository;
  private final AssessmentObjectiveDetailRepository assessmentObjectiveDetailRepository;
  private final AssessmentObjectiveDetailRepository aoDetailRepository;
  private final TermRepository termRepository;
  private final StrapiService strapiService;
  private final ContentService contentService;
  private final StudentRepository studentRepository;
  private final AoStudentDetailRepository aoStudentDetailRepository;
  private final AoStudentRepository aoStudentRepository;
  private final PrePrimaryAprService prePrimaryAprService;
  private final TermService termService;
  private final AoRemarkRepository aoRemarkRepository;
  private final List<String> eyGradeSlugs = List.of("nur", "lkg", "ukg");
  private final String DPS_AEROCITY_ORGSLUG = "del189476";
  private final String MATHEMATICS = "MATHEMATICS";
  private final String NUMERACY = "NUMERACY";
  private final UserRepository userRepository;
  private final SubjectsMetadataStudentsRepository subjectsMetadataStudentsRepository;

  public AssessmentObjectiveDto.AssessmentObjectiveResponse createAssessmentObjective(
      String orgSlug, AssessmentObjectiveDto.AssessmentObjectiveRequest request) {
    var term = validateTerm(request.termId());
    var assessmentObjective =
        assessmentObjectiveRepository
            .findByOrgSlugAndBoardAndGradeAndSubjectAndTermAndDeletedAtIsNull(
                orgSlug, request.boardSlug(), request.gradeSlug(), request.subjectSlug(), term);
    if (assessmentObjective.isPresent()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.AoAlreadyExists");
    }
    var newAssessmentObjective = buildAssessmentObjective(request, new AssessmentObjective());
    newAssessmentObjective.setOrgSlug(orgSlug);
    var savedAo = assessmentObjectiveRepository.save(newAssessmentObjective);
    return buildAssessmentObjectiveResponse(savedAo);
  }

  private AssessmentObjective buildAssessmentObjective(
      AssessmentObjectiveDto.AssessmentObjectiveRequest request,
      AssessmentObjective assessmentObjective) {
    assessmentObjective.setTerm(validateTerm(request.termId()));
    assessmentObjective.setSeqNo(request.seqNo());
    assessmentObjective.setSubjectName(strapiService.getSubjectNameBySlug(request.subjectSlug()));
    assessmentObjective.setGradeName(contentService.getGradeNameBySlug(request.gradeSlug()));
    assessmentObjective.setBoardName(
        strapiService.getEduBoardBySlug(request.boardSlug()).getName());
    assessmentObjective.setBoard(request.boardSlug());
    assessmentObjective.setGrade(request.gradeSlug());
    assessmentObjective.setSubject(request.subjectSlug());
    return assessmentObjective;
  }

  private Term validateTerm(long termId) {
    return termRepository
        .findById(termId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidTerm"));
  }

  public List<AssessmentObjectiveDto.AssessmentObjectiveResponse> getAssessmentObjective(
      String orgSlug) {
    var assessmentObjectives =
        assessmentObjectiveRepository.findByOrgSlugAndDeletedAtIsNullOrderByCreatedAtDesc(orgSlug);
    return assessmentObjectives.stream().map(this::buildAssessmentObjectiveResponse).toList();
  }

  private AssessmentObjectiveDto.AssessmentObjectiveResponse buildAssessmentObjectiveResponse(
      AssessmentObjective assessmentObjective) {
    return AssessmentObjectiveDto.AssessmentObjectiveResponse.builder()
        .id(assessmentObjective.getId())
        .termName(assessmentObjective.getTerm().getName())
        .termId(assessmentObjective.getTerm().getId())
        .subjectName(assessmentObjective.getSubjectName())
        .gradeName(assessmentObjective.getGradeName())
        .boardName(assessmentObjective.getBoardName())
        .boardSlug(assessmentObjective.getBoard())
        .gradeSlug(assessmentObjective.getGrade())
        .subjectSlug(assessmentObjective.getSubject())
        .seqNo(assessmentObjective.getSeqNo())
        .build();
  }

  public AssessmentObjectiveDto.AssessmentObjectiveResponse getAssessmentObjectiveById(
      String orgSlug, long aOId) {
    var assessmentObjective = validateAssessmentObjective(aOId, orgSlug);
    return buildAssessmentObjectiveResponse(assessmentObjective);
  }

  private AssessmentObjective validateAssessmentObjective(long id, String orgSlug) {
    return assessmentObjectiveRepository
        .findByIdAndOrgSlugAndDeletedAtIsNull(id, orgSlug)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.AssessmentNotFound"));
  }

  public void updateAssessmentObjectiveById(
      String orgSlug,
      long aOId,
      AssessmentObjectiveDto.AssessmentObjectiveRequest assessmentObjectiveRequest) {
    var assessmentObjective = validateAssessmentObjective(aOId, orgSlug);
    var updatedAssessmentObjective =
        buildAssessmentObjective(assessmentObjectiveRequest, assessmentObjective);
    assessmentObjectiveRepository.save(updatedAssessmentObjective);
  }

  public void deleteAssessmentObjective(String orgSlug, long aOId) {
    var assessmentObjective = validateAssessmentObjective(aOId, orgSlug);
    assessmentObjective.setDeletedAt(new Date());
    assessmentObjective.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    assessmentObjectiveRepository.save(assessmentObjective);
  }

  public void createAssessmentObjectiveDetail(
      String orgSlug, long aOId, AssessmentObjectiveDto.AoDetailRequest detailRequest) {

    var assessmentObjective = validateAssessmentObjective(aOId, orgSlug);
    var isExistsByText =
        aoDetailRepository.existsByTextAndAssessmentObjective(
            detailRequest.text(), assessmentObjective);
    if (isExistsByText) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Text can not be duplicated");
    }
    var assessmentObjectiveDetail =
        AssessmentObjectiveDetail.builder()
            .assessmentObjective(assessmentObjective)
            .text(detailRequest.text())
            .publishedAt(LocalDateTime.now())
            .build();
    assessmentObjective.setAssessmentObjectiveDetails(
        new ArrayList<>(List.of(assessmentObjectiveDetail)));
    assessmentObjectiveRepository.save(assessmentObjective);
  }

  public void deleteAssessmentObjectiveDetail(String orgSlug, Long aOId, long detailId) {
    var assessmentObjective = validateAssessmentObjective(aOId, orgSlug);
    var aoDetail =
        assessmentObjective.getAssessmentObjectiveDetails().stream()
            .filter(detail -> detail.getId().equals(detailId))
            .findAny()
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.InvalidAoDetail"));

    aoDetail.setDeletedAt(new Date());
    aoDetail.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    aoDetailRepository.save(aoDetail);
  }

  public List<AssessmentObjectiveDto.AssessmentObjectiveDetailResponse>
      getAssessmentObjectiveDetails(String orgSlug, Long aOId) {
    var assessmentObjective = validateAssessmentObjective(aOId, orgSlug);
    var details =
        aoDetailRepository.findAllByAssessmentObjectiveAndDeletedAtIsNullOrderByCreatedAtDesc(
            assessmentObjective);
    return details.stream().filter(Objects::nonNull).map(this::buildAoDetailResponse).toList();
  }

  private AssessmentObjectiveDto.AssessmentObjectiveDetailResponse buildAoDetailResponse(
      AssessmentObjectiveDetail details) {
    return AssessmentObjectiveDto.AssessmentObjectiveDetailResponse.builder()
        .id(details.getId())
        .text(details.getText())
        .publishedAt(
            Objects.nonNull(details.getPublishedAt())
                ? DateTimeUtil.convertIso8601ToEpoch(details.getPublishedAt())
                : null)
        .build();
  }

  public void createAoStudent(AssessmentObjectiveDto.AssessmentObjectiveStudentRequest request) {
    var assessmentObjective =
        assessmentObjectiveRepository
            .findById(request.aoId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.NO_RECORD_FOUND, "error.AssessmentNotFound"));
    var student =
        studentRepository
            .findById(request.studentId())
            .orElseThrow(
                () ->
                    new ApiException(
                        InternalErrorCodes.NO_RECORD_FOUND, "error.StudentFind.StudentId"));
    var termAssessment = termService.validateTermAssessment(request.termAssessmentId());
    var isAoStudentPresent =
        aoStudentRepository.findByAoIdAndStudentId(request.aoId(), request.studentId());
    if (isAoStudentPresent.isPresent()) {
      var aoStudent = isAoStudentPresent.get();
      aoStudent.setAreaOfFocus(request.areaOfFocus());
      aoStudent.setAreaOfStrength(request.areaOfStrength());
      aoStudent.setTermAssessment(termAssessment);
      aoStudent.setRemarks(request.remarks());
      aoStudentRepository.save(aoStudent);
      aoStudent.setAoStudentDetails(buildStudentAo(request, isAoStudentPresent.get()));
      aoStudentRepository.save(aoStudent);
    } else {
      var aoStudent = new AoStudent();
      aoStudent.setStudentId(student.getId());
      aoStudent.setAoId(assessmentObjective.getId());
      aoStudent.setAreaOfFocus(request.areaOfFocus());
      aoStudent.setTermAssessment(termAssessment);
      aoStudent.setAreaOfStrength(request.areaOfStrength());
      aoStudent.setRemarks(request.remarks());
      aoStudentRepository.save(aoStudent);
      aoStudent.setAoStudentDetails(buildStudentAo(request, aoStudent));
      aoStudentRepository.save(aoStudent);
    }
  }

  public List<AoStudentDetail> buildStudentAo(
      AssessmentObjectiveDto.AssessmentObjectiveStudentRequest request, AoStudent aoStudent) {
    List<AoStudentDetail> students = new ArrayList<>();

    request
        .assessmentObjectiveStudents()
        .forEach(
            aosRequest -> {
              var aod =
                  assessmentObjectiveDetailRepository
                      .findById(aosRequest.AoDetailId())
                      .orElseThrow(
                          () ->
                              new ApiException(
                                  InternalErrorCodes.NO_RECORD_FOUND,
                                  "error.AssessmentDetailNotFound"));
              if (aoStudent.getAoStudentDetails() == null) {
                students.add(
                    AoStudentDetail.builder()
                        .aoStudent(aoStudent)
                        .assessmentObjectiveDetail(aod)
                        .color(aosRequest.color())
                        .build());
              } else {
                var optionalAoStudentPresent =
                    aoStudent.getAoStudentDetails().stream()
                        .filter(
                            sd ->
                                sd.getAssessmentObjectiveDetail()
                                    .getId()
                                    .equals(aosRequest.AoDetailId()))
                        .findAny();

                optionalAoStudentPresent.ifPresentOrElse(
                    sd -> {
                      sd.setColor(aosRequest.color());
                      students.add(sd);
                    },
                    () ->
                        students.add(
                            AoStudentDetail.builder()
                                .aoStudent(aoStudent)
                                .assessmentObjectiveDetail(aod)
                                .color(aosRequest.color())
                                .build()));
              }
            });
    return students;
  }

  public AssessmentObjectiveDto.AoStudentCreationResponse getAoDetails(
      String orgSlug, String boardSlug, String gradeSlug, String subject, Long termId) {
    var term = validateTerm(termId);
    var assessmentObjective =
        assessmentObjectiveRepository
            .findByOrgSlugAndBoardAndGradeAndSubjectAndTermAndDeletedAtIsNull(
                orgSlug, boardSlug, gradeSlug, subject, term)
            .orElseThrow(
                () ->
                    new ApiException(InternalErrorCodes.NO_RECORD_FOUND, "error.Empty.AoDetails"));
    var details =
        assessmentObjective.getAssessmentObjectiveDetails().stream()
            .filter(d -> Objects.nonNull(d.getPublishedAt()) && Objects.isNull(d.getDeletedAt()))
            .toList();
    if (details.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          "Assessment Objectives not created or published for"
              + assessmentObjective.getSubjectName());
    }

    var assessmentDetail = details.get(0).getAssessmentObjective();
    return AssessmentObjectiveDto.AoStudentCreationResponse.builder()
        .termId(termId)
        .grade(assessmentDetail.getGrade())
        .aoName(assessmentDetail.getSubjectName())
        .assessmentObjectiveId(assessmentDetail.getId())
        .aoDetails(buildAoDetails(details))
        .build();
  }

  public List<AssessmentObjectiveDto.AssessmentObjectiveDetailResponse> buildAoDetails(
      List<AssessmentObjectiveDetail> aoResponse) {
    List<AssessmentObjectiveDto.AssessmentObjectiveDetailResponse> aoResponses = new ArrayList<>();
    for (AssessmentObjectiveDetail aoDetail : aoResponse) {
      aoResponses.add(
          AssessmentObjectiveDto.AssessmentObjectiveDetailResponse.builder()
              .text(aoDetail.getText())
              .publishedAt(
                  Objects.nonNull(aoDetail.getPublishedAt())
                      ? DateTimeUtil.convertIso8601ToEpoch(aoDetail.getPublishedAt())
                      : null)
              .id(aoDetail.getId())
              .build());
    }
    return aoResponses;
  }

  public AssessmentObjectiveDto.AssessmentObjectiveStudentRequest getStudentAos(
      Long aoId, Long studentId) {
    var aoStudent = aoStudentRepository.findByAoIdAndStudentId(aoId, studentId);
    if (aoStudent.isEmpty()) {
      log.info("student not present with aoStudentId {} and studentId {}", aoStudent, studentId);
      return null;
    }
    var student = aoStudent.get();
    return AssessmentObjectiveDto.AssessmentObjectiveStudentRequest.builder()
        .studentId(student.getStudentId())
        .aoId(student.getAoId())
        .areaOfFocus(student.getAreaOfFocus())
        .areaOfStrength(student.getAreaOfStrength())
        .remarks(student.getRemarks())
        .assessmentObjectiveStudents(buildAoStudentDetails(student.getAoStudentDetails()))
        .build();
  }

  public List<AssessmentObjectiveDto.AssessmentObjectiveStudent> buildAoStudentDetails(
      List<AoStudentDetail> aoStudentDetail) {
    List<AssessmentObjectiveDto.AssessmentObjectiveStudent> aoStudentDetails = new ArrayList<>();
    for (AoStudentDetail studentDetail : aoStudentDetail) {
      aoStudentDetails.add(
          AssessmentObjectiveDto.AssessmentObjectiveStudent.builder()
              .AoDetailId(studentDetail.getAssessmentObjectiveDetail().getId())
              .color(studentDetail.getColor())
              .build());
    }
    return aoStudentDetails;
  }

  public void updateAoDetailPublish(Long aoDetailId) {
    var aoDetail = validateAoDetailById(aoDetailId);
    if (Objects.isNull(aoDetail.getPublishedAt())) {
      aoDetail.setPublishedAt(LocalDateTime.now());
    } else {
      aoDetail.setPublishedAt(null);
    }
    aoDetailRepository.save(aoDetail);
  }

  private AssessmentObjectiveDetail validateAoDetailById(Long aoDetailId) {
    return aoDetailRepository
        .findById(aoDetailId)
        .orElseThrow(
            () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.AoDetailNotFound"));
  }

  public ReportCardConfigDto.AoReport getEyAssessmentReports(
      Student student, Long termId, String orgSlug) {

    List<AoStudent> aoStudents =
        aoStudentRepository.getDataByStudentAndTerm(student.getId(), termId);

    var aoStudentMap = aoStudents.stream().collect(Collectors.toMap(AoStudent::getAoId, os -> os));

    var aoIds =
        eyGradeSlugs.contains(student.getSection().getGradeSlug())
            ? aoStudents.stream()
                .filter(
                    aoStudent ->
                        !StringUtils.isEmpty(aoStudent.getAreaOfFocus())
                            && !StringUtils.isEmpty(aoStudent.getAreaOfStrength()))
                .map(AoStudent::getAoId)
                .toList()
            : aoStudents.stream().map(AoStudent::getAoId).toList();

    Term term = validateTerm(termId);

    var assessmentObjectives =
        assessmentObjectiveRepository.findAllByIdInAndTermAndDeletedAtIsNull(aoIds, term);

    var aoTables =
        assessmentObjectives.stream()
            .sorted(
                Comparator.comparing(
                    AssessmentObjective::getSeqNo, Comparator.nullsLast(Comparator.naturalOrder())))
            .map(
                ao -> {
                  var aoStudent = Optional.ofNullable(aoStudentMap.get(ao.getId()));
                  var aosValue = aoStudent.map(AoStudent::getAreaOfStrength).orElse(null);
                  var aofValue = aoStudent.map(AoStudent::getAreaOfFocus).orElse(null);

                  return ReportCardConfigDto.AoTable.builder()
                      .title(
                          Objects.nonNull(orgSlug)
                                  && orgSlug.equals(DPS_AEROCITY_ORGSLUG)
                                  && ao.getSubjectName().equalsIgnoreCase(MATHEMATICS)
                              ? NUMERACY
                              : ao.getSubjectName())
                      .subjectSlug(ao.getSubject())
                      .aofValue(Objects.nonNull(aofValue) ? aofValue.replace(".,", ".") : null)
                      .aosValue(Objects.nonNull(aosValue) ? aosValue.replace(".,", ".") : null)
                      .aorValue(aoStudent.map(AoStudent::getRemarks).orElse(null))
                      .aoDetails(
                          buildStudentAoDetails(
                              ao.getAssessmentObjectiveDetails(),
                              aoStudent.isPresent()
                                  ? aoStudent.get().getAoStudentDetails()
                                  : new ArrayList<>()))
                      .build();
                })
            .toList();

    return ReportCardConfigDto.AoReport.builder().aoTables(aoTables).build();
  }

  public ReportCardConfigDto.AoReport getEyAssessmentReportsWithTermId(
      Student student, Long termId, String orgSlug) {
    List<AoStudent> aoStudents = aoStudentRepository.findByStudentId(student.getId());

    var aoStudentMap = aoStudents.stream().collect(Collectors.toMap(AoStudent::getAoId, os -> os));

    var aoIds =
        eyGradeSlugs.contains(student.getSection().getGradeSlug())
            ? aoStudents.stream()
                .filter(
                    aoStudent ->
                        !StringUtils.isEmpty(aoStudent.getAreaOfFocus())
                            && !StringUtils.isEmpty(aoStudent.getAreaOfStrength()))
                .map(AoStudent::getAoId)
                .toList()
            : aoStudents.stream().map(AoStudent::getAoId).toList();

    var assessmentObjectives =
        assessmentObjectiveRepository.findAllByIdInAndTermIdAndDeletedAtIsNull(aoIds, termId);
    if (assessmentObjectives == null || assessmentObjectives.isEmpty()) {
      return null;
    }

    var aoTables =
        assessmentObjectives.stream()
            .sorted(
                Comparator.comparing(
                    AssessmentObjective::getSeqNo, Comparator.nullsLast(Comparator.naturalOrder())))
            .map(
                ao -> {
                  var aoStudent = Optional.ofNullable(aoStudentMap.get(ao.getId()));
                  var aosValue = aoStudent.map(AoStudent::getAreaOfStrength).orElse(null);
                  var aofValue = aoStudent.map(AoStudent::getAreaOfFocus).orElse(null);

                  return ReportCardConfigDto.AoTable.builder()
                      .title(
                          Objects.nonNull(orgSlug)
                                  && orgSlug.equals(DPS_AEROCITY_ORGSLUG)
                                  && ao.getSubjectName().equalsIgnoreCase(MATHEMATICS)
                              ? NUMERACY
                              : ao.getSubjectName())
                      .subjectSlug(ao.getSubject())
                      .aofValue(Objects.nonNull(aofValue) ? aofValue.replace(".,", ".") : null)
                      .aosValue(Objects.nonNull(aosValue) ? aosValue.replace(".,", ".") : null)
                      .aorValue(aoStudent.map(AoStudent::getRemarks).orElse(null))
                      .aoDetails(
                          buildStudentAoDetails(
                              ao.getAssessmentObjectiveDetails(),
                              aoStudent.isPresent()
                                  ? aoStudent.get().getAoStudentDetails()
                                  : new ArrayList<>()))
                      .build();
                })
            .toList();

    return ReportCardConfigDto.AoReport.builder().aoTables(aoTables).build();
  }

  private List<ReportCardConfigDto.AoDetail> buildStudentAoDetails(
      List<AssessmentObjectiveDetail> aoDetails, List<AoStudentDetail> aoStudentDetails) {
    var aoStudentDetailMap =
        aoStudentDetails.stream()
            .collect(Collectors.toMap(AoStudentDetail::getAssessmentObjectiveDetail, asd -> asd));

    return aoDetails.stream()
        .map(
            detail -> {
              var aoStudentDetail = Optional.ofNullable(aoStudentDetailMap.get(detail));
              return aoStudentDetail
                  .filter(
                      asd ->
                          asd.getColor() != null
                              && !asd.getColor().isEmpty()
                              && asd.getAssessmentObjectiveDetail().getPublishedAt() != null)
                  .map(
                      asd ->
                          ReportCardConfigDto.AoDetail.builder()
                              .learningStatement(detail.getText())
                              .indicator(asd.getColor())
                              .build())
                  .orElse(null);
            })
        .filter(Objects::nonNull)
        .toList();
  }

  public void updateAoDetail(Long aoDetailId, AssessmentObjectiveDto.AoDetailRequest request) {
    var aoDetail = validateAoDetailById(aoDetailId);
    aoDetail.setText(request.text());
    aoDetail.setUpdatedAt(Timestamp.valueOf(LocalDateTime.now()));
    assessmentObjectiveDetailRepository.save(aoDetail);
  }

  public AssessmentObjectiveDto.ValidAOSubjectResponse getValidateAoSubjects(
      String orgSlug, AssessmentObjectiveDto.AssessmentObjectiveRequest request) {
    var term = validateTerm(request.termId());

    var assessmentObjectives =
        assessmentObjectiveRepository
            .findByOrgSlugAndBoardAndGradeAndTermAndDeletedAtIsNullOrderBySeqNo(
                orgSlug, request.boardSlug(), request.gradeSlug(), term);
    if (request.studentAuthId() == null) {
      var subjects =
          assessmentObjectives.stream()
              .map(ao -> Subject.builder().name(ao.getSubjectName()).slug(ao.getSubject()).build())
              .toList();
      return AssessmentObjectiveDto.ValidAOSubjectResponse.builder().subjects(subjects).build();
    }
    var user =
        userRepository
            .findByAuthUserId(request.studentAuthId())
            .orElseThrow(
                () -> new ApiException(InternalErrorCodes.INVALID_REQUEST, "error.UserNotFound"));
    var studentMetadataSubjects =
        subjectsMetadataStudentsRepository.findByStudentId(user.getStudentInfo().getId());
    var subjectSlugs =
        studentMetadataSubjects.stream()
            .map(SubjectsMetadataStudents::getSubjectsMetaData)
            .map(SubjectsMetaData::getWexlSubjectSlug)
            .toList();
    var matchedSubjects =
        assessmentObjectives.stream()
            .filter(subject -> subjectSlugs.contains(subject.getSubject()))
            .toList();
    var subjects =
        matchedSubjects.stream()
            .map(ao -> Subject.builder().name(ao.getSubjectName()).slug(ao.getSubject()).build())
            .toList();
    return AssessmentObjectiveDto.ValidAOSubjectResponse.builder().subjects(subjects).build();
  }

  public List<AssessmentObjectiveDto.AoRemarkResponse> getAoRemarks(
      String orgSlug,
      String board,
      String grade,
      String subject,
      AreaRemark areaRemark,
      Long termId) {
    var term = validateTerm(termId);
    var aoRemarks =
        aoRemarkRepository
            .findAllByOrgSlugAndBoardAndGradeAndSubjectAndAreaRemarkAndTermIdOrderBySeqNumber(
                orgSlug, board, grade, subject, areaRemark, term.getId());
    return aoRemarks.stream()
        .map(
            aoRemark ->
                AssessmentObjectiveDto.AoRemarkResponse.builder()
                    .id(aoRemark.getId())
                    .remark(aoRemark.getRemark())
                    .areaRemark(aoRemark.getAreaRemark())
                    .build())
        .toList();
  }
}
