package com.wexl.dps.dto;

import com.wexl.gilico.dto.GilcoHolisticReportModeldto;
import java.util.List;
import lombok.Builder;

public class Gilco9th10thHolisticReportDto {

  @Builder
  public record Model(Header header, Body body) {}

  @Builder
  public record Header(
      String schoolName,
      String location,
      String academicYear,
      String reportTitle,
      String logoUrl,
      String backgroundImageUrl,
      String lastPageImageUrl) {}

  @Builder
  public record Body(
      String name,
      String className,
      String sectionName,
      String rollNo,
      String admissionNumber,
      String house,
      String dateOfBirth,
      Integer age,
      String address,
      String fatherName,
      String motherName,
      GilcoHolisticReportModeldto.AllAboutMe allAboutMe,
      Attendance attendance,
      Interests interests,
      MedicalProfile medicalProfile,
      List<SelfAndPeerAssessments> selfAndPeerAssessments,
      Support support,
      List<ScholosticMandatory> scholosticMandatory,
      List<ScholosticOptional> scholosticOptional,
      ScholasticAreas scholasticAreas,
      List<CoScholasticArea> coScholasticAreas,
      List<CoScholasticCategory> coScholasticCategories,
      String classRemarks,
      String principleRemarks) {}

  @Builder
  public record TableMarks(
      List<ScholosticMandatory> scholosticMandatory,
      List<ScholosticOptional> scholosticOptional,
      List<CoScholosticMandatory> coScholosticMandatory,
      List<CoScholosticOptional> coScholosticOptional) {}

  @Builder
  public record ScholasticAreas(List<Subject> subjects, List<SkillSubject> skillSubjects) {}

  @Builder
  public record Subject(
      String name,
      String penPaperTest,
      String notebookPortfolio,
      String subjectEnrichment,
      String multipleAssessment,
      String annualExam,
      String total,
      String grade) {}

  @Builder
  public record SkillSubject(
      String name, String theory, String practical, String total, String grade) {}

  @Builder
  public record CoScholasticArea(String name, String grade) {}

  @Builder
  public record CoScholasticCategory(String categoryName, List<CoScholasticSkill> skills) {}

  @Builder
  public record CoScholasticSkill(String skillName, String grade) {}

  @Builder
  public record CoScholastic(String skillName, List<Skill> skill) {}

  @Builder
  public record Skill(String subject, String grade) {}

  @Builder
  public record Attendance(Days workingDays, Days attendingDays, Days percentage) {}

  @Builder
  public record Days(
      String title,
      Long apr,
      Long may,
      Long jun,
      Long july,
      Long aug,
      Long sep,
      Long oct,
      Long nov,
      Long dec,
      Long jan,
      Long feb,
      Long mar) {}

  @Builder
  public record Interests(
      boolean reading,
      boolean dancing,
      boolean singing,
      boolean musicalInstrument,
      boolean sportsOrGames,
      boolean writing,
      boolean gardening,
      boolean yoga,
      boolean art,
      boolean craft,
      boolean cooking,
      boolean others,
      String specify) {}

  @Builder
  public record MedicalProfile(
      String bloodGroup,
      String height,
      String weight,
      String rightEyeSight,
      String leftEyeSight,
      String dental) {}

  @Builder
  public record SelfAndPeerAssessments(
      String skillName, String saTerm1, String saTerm2, String paTerm1, String paTerm2) {}

  @Builder
  public record Support(String learningSupport, String emotionalSupport, String physicalSupport) {}

  @Builder
  public record ScholosticMandatory(
      String subject,
      String term1Pa,
      String term1NAP,
      String term1Sa,
      String term1Ma,
      String term1Marks,
      String term1Total,
      String term2Pa,
      String term2NAP,
      String term2Sa,
      String term2Ma,
      String term2Marks,
      String term2Total) {}

  @Builder
  public record ScholosticOptional(
      String subject,
      String term1Pa,
      String term1Theory,
      String term1Practical,
      String term2Pa,
      String term2Theory,
      String term2Practical) {}

  @Builder
  public record CoScholosticMandatory(String subject, String grade) {}

  @Builder
  public record CoScholosticOptional(String subject, String grade) {}
}
