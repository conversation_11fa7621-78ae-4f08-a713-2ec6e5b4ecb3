package com.wexl.dps.reportcard;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.Gilco9th10thHolisticReportDto;
import com.wexl.dps.dto.Gillco3rd5thHolisticReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.gilico.service.GillcoHolisticReportCard;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.holisticreportcards.model.ProgressCard;
import com.wexl.holisticreportcards.repository.FacilitatorStudentsRepository;
import com.wexl.holisticreportcards.repository.ProgressCardRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.erp.attendance.dto.StudentsAttendanceReport;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class Gilco9th10thHolisticReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final GillcoHolisticReportCard gillcoHolisticReportCard;
  private final Gillco3rd5thHolisticReportCard gillco3rd5thHolisticReportCard;
  private final ProgressCardRepository progressCardRepository;
  private final ProgressCardService progressCardService;
  private final FacilitatorStudentsRepository facilitatorStudentsRepository;
  private final UserService userService;
  private final SectionAttendanceRepository sectionAttendanceRepository;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;

  @Value("classpath:gilco-9th10th-co-scholastic.json")
  private Resource gilco9th10thCoScholasticResource;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("gilco-holistic-9th-10th-term1-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = gillco3rd5thHolisticReportCard.buildHolisticHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  public Gilco9th10thHolisticReportDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var section = student.getSection();
    var mother =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    Optional<StudentAttributeValueModel> dateOfBirthOpt =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var dateOfBirth = dateOfBirthOpt.map(StudentAttributeValueModel::getValue).orElse(null);
    Optional<StudentAttributeValueModel> house =
        reportCardService.getStudentAttributeValue(student, "house");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residential_address");
    Integer age = gillcoHolisticReportCard.getAge(dateOfBirth);
    var studentData = progressCardRepository.findByStudentId(student.getId());
    List<ProgressCardDto.Competencies> competencies =
        progressCardService.getCompetencyDetails(section.getGradeSlug(), orgSlug, student);

    var data = buildTableMarks(student, orgSlug);
    Map<String, Integer> range = gillco3rd5thHolisticReportCard.getConvertedMarchToFebRange();
    var convertedFromDate = range.get("convertedFromDate");
    var convertedToDate = range.get("convertedToDate");

    var facilitatorStudentOpt = facilitatorStudentsRepository.findByStudentId(student.getId());
    var facilitator =
        facilitatorStudentOpt.isPresent() ? facilitatorStudentOpt.get().getFacilitator() : null;

    return Gilco9th10thHolisticReportDto.Body.builder()
        .name(userService.getNameByUserInfo(student.getUserInfo()))
        .className(student.getSection().getGradeName())
        .sectionName(student.getSection().getName())
        .rollNo(student.getClassRollNumber())
        .admissionNumber(student.getRollNumber())
        .house(house.map(StudentAttributeValueModel::getValue).orElse(null))
        .dateOfBirth(dateOfBirth)
        .age(age)
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(father)
        .motherName(mother)
        .allAboutMe(gillcoHolisticReportCard.buildAllAboutMe(studentData))
        .attendance(buildAttendance(student, orgSlug, convertedFromDate, convertedToDate))
        .interests(buildInterests(studentData))
        .medicalProfile(buildMedicalProfile(studentData))
        .selfAndPeerAssessments(buildSelfAndPeerAssessments(student, orgSlug))
        .scholosticMandatory(data.scholosticMandatory())
        .scholosticOptional(data.scholosticOptional())
        .scholasticAreas(buildScholasticAreas(data))
        .coScholasticAreas(buildCoScholasticAreas(data))
        .coScholasticCategories(buildCoScholasticCategories(data))
        .classRemarks(facilitator != null ? facilitator.getClassFacilitatorRemarks() : null)
        .principleRemarks(facilitator != null ? facilitator.getPrincipalRemarks() : null)
        .build();
  }

  public Gilco9th10thHolisticReportDto.TableMarks buildTableMarks(Student student, String orgSlug) {
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Arrays.asList("t1"), student.getSection().getGradeSlug());

    log.info("buildTableMarks: Found {} term assessments for grade {} and term t1",
             termAssessments.size(), student.getSection().getGradeSlug());

    termAssessments.forEach(assessment ->
        log.debug("Term assessment: id={}, name={}, slug={}",
                  assessment.getId(), assessment.getName(), assessment.getSlug()));

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);

    log.info("buildTableMarks: Retrieved {} records for student {} with {} term assessments",
             data != null ? data.size() : 0, student.getId(), termAssessmentIds.size());

    if (data != null && !data.isEmpty()) {
      log.debug("Sample assessment data:");
      data.stream().limit(5).forEach(record ->
          log.debug("Record: subject={}, assessmentSlug={}, assessmentName={}, marks={}, category={}, type={}",
                    record.getSubjectName(), record.getAssessmentSlug(), record.getAssessmentName(),
                    record.getMarks(), record.getCategory(), record.getType()));
    }

    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var sortedData =
        sortTable(
            scholosticMandatory(scholasticDataList, orgSlug),
            scholosticOptional(optionalData, orgSlug),
            coScholosticMandatory(coScholasticMandatoryData, orgSlug),
            coScholosticOptional(coScholasticOptionalData, orgSlug));

    return sortedData;
  }

  private Gilco9th10thHolisticReportDto.TableMarks sortTable(
      List<Gilco9th10thHolisticReportDto.ScholosticMandatory> scholosticMandatory,
      List<Gilco9th10thHolisticReportDto.ScholosticOptional> scholosticOptional,
      List<Gilco9th10thHolisticReportDto.CoScholosticMandatory> coScholosticMandatory,
      List<Gilco9th10thHolisticReportDto.CoScholosticOptional> coScholosticOptional) {
    return Gilco9th10thHolisticReportDto.TableMarks.builder()
        .scholosticMandatory(scholosticMandatory)
        .scholosticOptional(scholosticOptional)
        .coScholosticMandatory(coScholosticMandatory)
        .coScholosticOptional(coScholosticOptional)
        .build();
  }

  private List<Gilco9th10thHolisticReportDto.ScholosticOptional> scholosticOptional(
      List<LowerGradeReportCardData> optionalData, String orgSlug) {

    List<Gilco9th10thHolisticReportDto.ScholosticOptional> marksList = new ArrayList<>();

    var scholasticOptDataMap =
        optionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticOptDataMap.forEach(
        (subject, scholasticData) -> {
          var theoryMarks = getTermMarks("theory", scholasticData);
          var practicalMarks = getTermMarks("practical", scholasticData);

          marksList.add(
              Gilco9th10thHolisticReportDto.ScholosticOptional.builder()
                  .subject(subject)
                  .term1Theory(theoryMarks)
                  .term1Practical(practicalMarks)
                  .build());
        });

    return marksList;
  }

  private List<Gilco9th10thHolisticReportDto.ScholosticMandatory> scholosticMandatory(
      List<LowerGradeReportCardData> reportCardData, String orgSlug) {

    List<Gilco9th10thHolisticReportDto.ScholosticMandatory> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          log.debug("Processing subject: {} with {} assessment records", subject, scholasticData.size());

          // Log all available assessments for this subject
          scholasticData.forEach(data ->
              log.debug("  Assessment: name='{}', slug='{}', marks={}, attended={}",
                        data.getAssessmentName(), data.getAssessmentSlug(), data.getMarks(), data.getIsAttended()));

          // Try flexible assessment slug matching
          var term1Pa = getBestPeriodicAssessmentFlexible(scholasticData);
          var term1NAP = getBestScoreOutOfFiveFlexible(scholasticData);
          var term1Sa = getSubjectEnrichmentMarks("se1", scholasticData);
          var term1Ma = getMultipleAssessmentMarks("se1", scholasticData);
          var term1Marks = getTermMarksFlexible(scholasticData);

          // If all specific methods fail, try to get any available marks
          if (term1Pa == null && term1NAP == null && term1Sa == null && term1Ma == null && term1Marks == null) {
            log.warn("No marks found for subject {} using specific methods, trying fallback", subject);
            var anyMarks = getAnyAvailableMarks(scholasticData);
            if (anyMarks != null) {
              term1Marks = anyMarks; // Assign to main exam field as fallback
              log.debug("Using fallback marks for subject {}: {}", subject, anyMarks);
            }
          }

          var term2Pa = getTermMarks("penpapertest", scholasticData);
          var term2NAP = getTermMarks("nb2", scholasticData);
          var term2Sa = getTermMarks("se2", scholasticData);
          var term2Ma = getTermMarks("se2", scholasticData);
          var term2Marks = getTermMarks("ye", scholasticData);

          log.debug("Subject {}: term1Pa={}, term1NAP={}, term1Sa={}, term1Ma={}, term1Marks={}",
                    subject, term1Pa, term1NAP, term1Sa, term1Ma, term1Marks);

          var term1Total = sumMarks(term1Pa, term1NAP, term1Sa, term1Ma, term1Marks);
          var term2Total = sumMarks(term2Pa, term2NAP, term2Sa, term2Ma, term2Marks);

          log.debug("Subject {}: term1Total={}, term2Total={}", subject, term1Total, term2Total);

          marksList.add(
              Gilco9th10thHolisticReportDto.ScholosticMandatory.builder()
                  .subject(subject)
                  .term1Pa(term1Pa)
                  .term1NAP(term1NAP)
                  .term1Sa(term1Sa)
                  .term1Ma(term1Ma)
                  .term1Marks(term1Marks)
                  .term1Total(term1Total)
                  .term2Pa(term2Pa)
                  .term2NAP(term2NAP)
                  .term2Sa(term2Sa)
                  .term2Ma(term2Ma)
                  .term2Marks(term2Marks)
                  .term2Total(term2Total)
                  .build());
        });

    return marksList;
  }

  private List<Gilco9th10thHolisticReportDto.CoScholosticMandatory> coScholosticMandatory(
      List<LowerGradeReportCardData> coScholasticMandatoryData, String orgSlug) {

    List<Gilco9th10thHolisticReportDto.CoScholosticMandatory> marksList = new ArrayList<>();
    List<CoScholasticConfig> coScholasticConfigs =
        readCoScholasticConfig(gilco9th10thCoScholasticResource);

    var coScholasticDataMap =
        coScholasticMandatoryData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .filter(rcd -> rcd.getMarks() != null || rcd.getRemarks() != null)
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    coScholasticConfigs.forEach(
        config -> {
          String subjectName = config.getSubjectName();
          List<LowerGradeReportCardData> subjectData = coScholasticDataMap.get(subjectName);

          if (subjectData != null && !subjectData.isEmpty()) {
            var grade = calculateCoScholasticGrade(subjectData);
            marksList.add(
                Gilco9th10thHolisticReportDto.CoScholosticMandatory.builder()
                    .subject(subjectName)
                    .grade(grade)
                    .build());
          } else {
            marksList.add(
                Gilco9th10thHolisticReportDto.CoScholosticMandatory.builder()
                    .subject(subjectName)
                    .grade("")
                    .build());
          }
        });

    return marksList;
  }

  private List<Gilco9th10thHolisticReportDto.CoScholosticOptional> coScholosticOptional(
      List<LowerGradeReportCardData> coScholasticOptionalData, String orgSlug) {

    List<Gilco9th10thHolisticReportDto.CoScholosticOptional> marksList = new ArrayList<>();
    List<CoScholasticConfig> coScholasticConfigs =
        readCoScholasticConfig(gilco9th10thCoScholasticResource);

    var coScholasticDataMap =
        coScholasticOptionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .filter(rcd -> rcd.getMarks() != null || rcd.getRemarks() != null)
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    coScholasticConfigs.forEach(
        config -> {
          String subjectName = config.getSubjectName();
          List<LowerGradeReportCardData> subjectData = coScholasticDataMap.get(subjectName);

          if (subjectData != null && !subjectData.isEmpty()) {
            var grade = calculateCoScholasticGrade(subjectData);
            marksList.add(
                Gilco9th10thHolisticReportDto.CoScholosticOptional.builder()
                    .subject(subjectName)
                    .grade(grade)
                    .build());
          } else {
            marksList.add(
                Gilco9th10thHolisticReportDto.CoScholosticOptional.builder()
                    .subject(subjectName)
                    .grade("")
                    .build());
          }
        });

    return marksList;
  }

  private String calculateCoScholasticGrade(List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty()) {
      return "";
    }

    var data = dataList.stream().findFirst().orElse(null);
    if (data == null) {
      return "";
    }

    String grade = calculateCoScholasticGradeFromMarks(data.getMarks());
    if (grade == null || grade.isBlank()) {
      grade =
          Optional.ofNullable(data.getRemarks())
              .filter(r -> r.length() >= 2)
              .map(r -> r.substring(0, 2))
              .orElse("");
    }
    return grade;
  }

  private String calculateCoScholasticGradeFromMarks(Double marks) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(marks));
  }

  private String calculateScholasticGrade(String totalMarks) {
    if (totalMarks == null || totalMarks.isEmpty() || totalMarks.equals("0.0")) {
      return "";
    }

    try {
      double marks = Double.parseDouble(totalMarks);
      return pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(marks));
    } catch (NumberFormatException e) {
      return "";
    }
  }

  private String sumMarks(String... marks) {
    double total =
        Arrays.stream(marks)
            .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
            .mapToDouble(Double::parseDouble)
            .sum();

    return String.format("%.1f", total);
  }

  private String getTermMarks(String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty() || assessmentSlug == null) {
      log.debug("getTermMarks: null/empty input - assessmentSlug: {}, dataList size: {}",
                assessmentSlug, dataList != null ? dataList.size() : 0);
      return null;
    }

    // Debug: Log all available assessment slugs
    log.debug("getTermMarks: Looking for assessmentSlug '{}' in {} records", assessmentSlug, dataList.size());
    dataList.forEach(data ->
        log.debug("Available assessment: slug='{}', name='{}', marks={}, attended={}",
                  data.getAssessmentSlug(), data.getAssessmentName(), data.getMarks(), data.getIsAttended()));

    var matched =
        dataList.stream()
            .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
            .findFirst();

    if (matched.isEmpty()) {
      log.warn("getTermMarks: No match found for assessmentSlug '{}' in {} records", assessmentSlug, dataList.size());
      return null;
    }

    var data = matched.get();
    log.debug("getTermMarks: Found match - slug='{}', marks={}, attended={}",
              data.getAssessmentSlug(), data.getMarks(), data.getIsAttended());

    if ("true".equalsIgnoreCase(data.getIsAttended())) {
      Double marks = data.getMarks();
      String result = marks != null ? String.format("%.1f", marks) : null;
      log.debug("getTermMarks: Returning marks result: {}", result);
      return result;
    } else {
      String remarks = data.getRemarks();
      String result = (remarks == null || remarks.isBlank())
          ? "AB"
          : remarks.substring(0, Math.min(2, remarks.length()));
      log.debug("getTermMarks: Returning remarks result: {}", result);
      return result;
    }
  }

  public Gilco9th10thHolisticReportDto.ScholasticAreas buildScholasticAreas(
      Gilco9th10thHolisticReportDto.TableMarks tableMarks) {

    List<Gilco9th10thHolisticReportDto.Subject> subjects =
        tableMarks.scholosticMandatory() != null
            ? tableMarks.scholosticMandatory().stream()
                .map(
                    mandatory -> {
                      String grade = calculateScholasticGrade(mandatory.term1Total());

                      return Gilco9th10thHolisticReportDto.Subject.builder()
                          .name(mandatory.subject())
                          .penPaperTest(mandatory.term1Pa() != null ? mandatory.term1Pa() : "")
                          .notebookPortfolio(
                              mandatory.term1NAP() != null ? mandatory.term1NAP() : "")
                          .subjectEnrichment(mandatory.term1Sa() != null ? mandatory.term1Sa() : "")
                          .multipleAssessment(
                              mandatory.term1Ma() != null ? mandatory.term1Ma() : "")
                          .annualExam(mandatory.term1Marks() != null ? mandatory.term1Marks() : "")
                          .total(mandatory.term1Total() != null ? mandatory.term1Total() : "")
                          .grade(grade)
                          .build();
                    })
                .collect(Collectors.toList())
            : new ArrayList<>();

    List<Gilco9th10thHolisticReportDto.SkillSubject> skillSubjects =
        tableMarks.scholosticOptional() != null
            ? tableMarks.scholosticOptional().stream()
                .map(
                    optional -> {
                      String theoryMarks =
                          optional.term1Theory() != null ? optional.term1Theory() : "0";
                      String practicalMarks =
                          optional.term1Practical() != null ? optional.term1Practical() : "0";
                      String total = sumMarks(theoryMarks, practicalMarks);
                      String grade = calculateScholasticGrade(total);

                      return Gilco9th10thHolisticReportDto.SkillSubject.builder()
                          .name(optional.subject())
                          .theory(theoryMarks)
                          .practical(practicalMarks)
                          .total(total)
                          .grade(grade)
                          .build();
                    })
                .collect(Collectors.toList())
            : new ArrayList<>();

    return Gilco9th10thHolisticReportDto.ScholasticAreas.builder()
        .subjects(subjects)
        .skillSubjects(skillSubjects)
        .build();
  }

  public List<Gilco9th10thHolisticReportDto.CoScholasticArea> buildCoScholasticAreas(
      Gilco9th10thHolisticReportDto.TableMarks tableMarks) {

    List<Gilco9th10thHolisticReportDto.CoScholasticArea> coScholasticAreas = new ArrayList<>();

    if (tableMarks.coScholosticMandatory() != null) {
      tableMarks
          .coScholosticMandatory()
          .forEach(
              mandatory -> {
                coScholasticAreas.add(
                    Gilco9th10thHolisticReportDto.CoScholasticArea.builder()
                        .name(mandatory.subject())
                        .grade(mandatory.grade() != null ? mandatory.grade() : "")
                        .build());
              });
    }

    if (tableMarks.coScholosticOptional() != null) {
      tableMarks
          .coScholosticOptional()
          .forEach(
              optional -> {
                coScholasticAreas.add(
                    Gilco9th10thHolisticReportDto.CoScholasticArea.builder()
                        .name(optional.subject())
                        .grade(optional.grade() != null ? optional.grade() : "")
                        .build());
              });
    }

    return coScholasticAreas;
  }

  public List<Gilco9th10thHolisticReportDto.CoScholasticCategory> buildCoScholasticCategories(
      Gilco9th10thHolisticReportDto.TableMarks tableMarks) {

    List<CoScholasticConfig> coScholasticConfigs =
        readCoScholasticConfig(gilco9th10thCoScholasticResource);
    List<Gilco9th10thHolisticReportDto.CoScholasticCategory> categories = new ArrayList<>();

    Map<String, List<CoScholasticConfig>> groupedConfigs =
        coScholasticConfigs.stream()
            .collect(
                Collectors.groupingBy(
                    CoScholasticConfig::getSubjectTitle, LinkedHashMap::new, Collectors.toList()));

    Map<String, String> categoryNames =
        Map.of(
            "art-education", "ART EDUCATION",
            "health-and-physical-education", "HEALTH & PHYSICAL EDUCATION",
            "disciplinary-traits", "DISCIPLINARY TRAITS");

    groupedConfigs.forEach(
        (subjectTitle, configs) -> {
          List<Gilco9th10thHolisticReportDto.CoScholasticSkill> skills = new ArrayList<>();

          List<Gilco9th10thHolisticReportDto.CoScholasticArea> flatAreas =
              buildCoScholasticAreas(tableMarks);

          configs.forEach(
              config -> {
                String grade =
                    flatAreas.stream()
                        .filter(area -> area.name().equals(config.getSubjectName()))
                        .map(Gilco9th10thHolisticReportDto.CoScholasticArea::grade)
                        .findFirst()
                        .orElse("");

                skills.add(
                    Gilco9th10thHolisticReportDto.CoScholasticSkill.builder()
                        .skillName(config.getSubjectName())
                        .grade(grade)
                        .build());
              });

          String categoryName =
              categoryNames.getOrDefault(subjectTitle, subjectTitle.toUpperCase());
          categories.add(
              Gilco9th10thHolisticReportDto.CoScholasticCategory.builder()
                  .categoryName(categoryName)
                  .skills(skills)
                  .build());
        });

    return categories;
  }

  private List<CoScholasticConfig> readCoScholasticConfig(Resource resource) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      return objectMapper.readValue(
          resource.getInputStream(),
          objectMapper
              .getTypeFactory()
              .constructCollectionType(List.class, CoScholasticConfig.class));
    } catch (IOException e) {
      throw new RuntimeException("Failed to read co-scholastic config from JSON", e);
    }
  }

  public static class CoScholasticConfig {
    private String subjectTitle;
    private String subjectName;

    public String getSubjectTitle() {
      return subjectTitle;
    }

    public void setSubjectTitle(String subjectTitle) {
      this.subjectTitle = subjectTitle;
    }

    public String getSubjectName() {
      return subjectName;
    }

    public void setSubjectName(String subjectName) {
      this.subjectName = subjectName;
    }
  }

  private Gilco9th10thHolisticReportDto.Interests buildInterests(ProgressCard studentData) {
    if (studentData == null) {
      return Gilco9th10thHolisticReportDto.Interests.builder().build();
    }
    var interests = gillco3rd5thHolisticReportCard.buildInterests(studentData);
    return Gilco9th10thHolisticReportDto.Interests.builder()
        .reading(interests.reading())
        .dancing(interests.dancing())
        .singing(interests.singing())
        .musicalInstrument(interests.musicalInstrument())
        .sportsOrGames(interests.sportsOrGames())
        .writing(interests.writing())
        .gardening(interests.gardening())
        .yoga(interests.yoga())
        .art(interests.art())
        .craft(interests.craft())
        .cooking(interests.cooking())
        .others(interests.others())
        .specify(interests.specify())
        .build();
  }

  public Gilco9th10thHolisticReportDto.MedicalProfile buildMedicalProfile(
      ProgressCard progressCard) {
    if (progressCard == null) {
      return Gilco9th10thHolisticReportDto.MedicalProfile.builder().build();
    }

    return Gilco9th10thHolisticReportDto.MedicalProfile.builder()
        .bloodGroup(progressCard.getBloodGroup())
        .height(progressCard.getTerm1Height())
        .weight(progressCard.getTerm1Weight())
        .rightEyeSight(progressCard.getEyesightR())
        .leftEyeSight(progressCard.getEyesightL())
        .dental(progressCard.getDental())
        .build();
  }

  private List<Gilco9th10thHolisticReportDto.SelfAndPeerAssessments> buildSelfAndPeerAssessments(
      Student student, String orgSlug) {
    List<Gillco3rd5thHolisticReportDto.SelfAndPeerAssessments> responseList =
        gillco3rd5thHolisticReportCard.buildSelfAndPeerAssessments(student, orgSlug);
    return responseList.stream()
        .map(
            item ->
                Gilco9th10thHolisticReportDto.SelfAndPeerAssessments.builder()
                    .skillName(item.skillName())
                    .saTerm1(item.saTerm1())
                    .saTerm2(item.saTerm2())
                    .paTerm1(item.paTerm1())
                    .paTerm2(item.paTerm2())
                    .build())
        .toList();
  }

  public Gilco9th10thHolisticReportDto.Attendance buildAttendance(
      Student student, String orgSlug, Integer fromDate, Integer toDate) {
    Section section = student.getSection();
    User user = student.getUserInfo();

    List<StudentsAttendanceReport> attendanceReports =
        sectionAttendanceRepository.getStudentMonthlyAttendance(
            orgSlug, section.getId(), user.getAuthUserId(), fromDate, toDate);

    Map<Integer, Long> presentMap = new HashMap<>();
    Map<Integer, Long> workingMap = new HashMap<>();
    Map<Integer, Long> percentageMap = new HashMap<>();

    for (StudentsAttendanceReport report : attendanceReports) {
      Integer month = report.getMonthNo();
      presentMap.put(month, report.getPresentDays());
      workingMap.put(month, report.getTotalWorkingDays());
      percentageMap.put(
          month,
          Math.round((double) report.getPresentDays() / report.getTotalWorkingDays() * 100.0));
    }

    return Gilco9th10thHolisticReportDto.Attendance.builder()
        .attendingDays(buildDays("No.of Days Attended", presentMap))
        .workingDays(buildDays("No.of Working Days", workingMap))
        .percentage(buildDays("No.of Working Days", percentageMap))
        .build();
  }

  private Gilco9th10thHolisticReportDto.Days buildDays(String title, Map<Integer, Long> dataMap) {
    return Gilco9th10thHolisticReportDto.Days.builder()
        .title(title)
        .apr(dataMap.getOrDefault(4, 0L))
        .may(dataMap.getOrDefault(5, 0L))
        .jun(dataMap.getOrDefault(6, 0L))
        .july(dataMap.getOrDefault(7, 0L))
        .aug(dataMap.getOrDefault(8, 0L))
        .sep(dataMap.getOrDefault(9, 0L))
        .oct(dataMap.getOrDefault(10, 0L))
        .nov(dataMap.getOrDefault(11, 0L))
        .dec(dataMap.getOrDefault(12, 0L))
        .jan(dataMap.getOrDefault(1, 0L))
        .feb(dataMap.getOrDefault(2, 0L))
        .mar(dataMap.getOrDefault(3, 0L))
        .build();
  }

  // Flexible methods that try multiple assessment slug patterns
  private String getTermMarksFlexible(List<LowerGradeReportCardData> dataList) {
    // Try different possible slugs for main exam marks
    String[] possibleSlugs = {"hye", "half-yearly", "midterm", "term1", "exam"};

    for (String slug : possibleSlugs) {
      String result = getTermMarks(slug, dataList);
      if (result != null) {
        log.debug("Found marks using slug '{}': {}", slug, result);
        return result;
      }
    }

    // If no specific slug works, try to find any assessment with "exam" or "test" in the name
    var examData = dataList.stream()
        .filter(x -> x.getAssessmentName() != null &&
                     (x.getAssessmentName().toLowerCase().contains("exam") ||
                      x.getAssessmentName().toLowerCase().contains("test") ||
                      x.getAssessmentName().toLowerCase().contains("term")))
        .filter(x -> "true".equalsIgnoreCase(x.getIsAttended()) && x.getMarks() != null)
        .findFirst();

    if (examData.isPresent()) {
      String result = String.format("%.1f", examData.get().getMarks());
      log.debug("Found exam marks by name matching: {}", result);
      return result;
    }

    return null;
  }

  private String getBestPeriodicAssessmentFlexible(List<LowerGradeReportCardData> dataList) {
    // Try different possible slugs for periodic assessment
    String[] possibleSlugs = {"penpapertest", "pa1", "periodic", "unit-test", "ut"};

    for (String slug : possibleSlugs) {
      String result = getTermMarks(slug, dataList);
      if (result != null) {
        log.debug("Found periodic assessment using slug '{}': {}", slug, result);
        return result;
      }
    }

    // Try to find by assessment name
    var paData = dataList.stream()
        .filter(x -> x.getAssessmentName() != null &&
                     (x.getAssessmentName().toLowerCase().contains("periodic") ||
                      x.getAssessmentName().toLowerCase().contains("unit") ||
                      x.getAssessmentName().toLowerCase().contains("paper")))
        .filter(x -> "true".equalsIgnoreCase(x.getIsAttended()) && x.getMarks() != null)
        .findFirst();

    if (paData.isPresent()) {
      String result = String.format("%.1f", paData.get().getMarks());
      log.debug("Found periodic assessment by name matching: {}", result);
      return result;
    }

    return null;
  }

  private String getBestScoreOutOfFiveFlexible(List<LowerGradeReportCardData> dataList) {
    // Try different possible slugs for portfolio/notebook
    String[] possibleSlugs = {"nb1", "portfolio", "notebook", "project"};

    for (String slug : possibleSlugs) {
      String result = getTermMarks(slug, dataList);
      if (result != null) {
        log.debug("Found portfolio/notebook using slug '{}': {}", slug, result);
        return result;
      }
    }

    // Try to find by assessment name
    var portfolioData = dataList.stream()
        .filter(x -> x.getAssessmentName() != null &&
                     (x.getAssessmentName().toLowerCase().contains("portfolio") ||
                      x.getAssessmentName().toLowerCase().contains("notebook") ||
                      x.getAssessmentName().toLowerCase().contains("project")))
        .filter(x -> "true".equalsIgnoreCase(x.getIsAttended()) && x.getMarks() != null)
        .findFirst();

    if (portfolioData.isPresent()) {
      String result = String.format("%.1f", portfolioData.get().getMarks());
      log.debug("Found portfolio/notebook by name matching: {}", result);
      return result;
    }

    return null;
  }

  private String getSubjectEnrichmentMarks(String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty() || assessmentSlug == null) {
      return null;
    }

    var matched = dataList.stream()
        .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
        .filter(x -> x.getAssessmentName() != null &&
                     x.getAssessmentName().toLowerCase().contains("subject enrichment"))
        .findFirst();

    if (matched.isEmpty()) {
      return null;
    }

    var data = matched.get();
    if ("true".equalsIgnoreCase(data.getIsAttended())) {
      Double marks = data.getMarks();
      return marks != null ? String.format("%.1f", marks) : null;
    } else {
      String remarks = data.getRemarks();
      return (remarks == null || remarks.isBlank()) ? "AB" : remarks.substring(0, Math.min(2, remarks.length()));
    }
  }

  private String getMultipleAssessmentMarks(String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty() || assessmentSlug == null) {
      return null;
    }

    var multipleAssessmentData = dataList.stream()
        .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
        .filter(x -> x.getAssessmentName() != null &&
                     x.getAssessmentName().toLowerCase().contains("multiple assessment"))
        .collect(Collectors.toList());

    if (multipleAssessmentData.isEmpty()) {
      return null;
    }

    // Calculate average of all Multiple Assessment entries
    double totalMarks = 0.0;
    int count = 0;

    for (LowerGradeReportCardData data : multipleAssessmentData) {
      if ("true".equalsIgnoreCase(data.getIsAttended()) && data.getMarks() != null) {
        totalMarks += data.getMarks();
        count++;
      }
    }

    if (count == 0) {
      return null;
    }

    double average = totalMarks / count;
    return String.format("%.1f", average);
  }

  private String getAnyAvailableMarks(List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty()) {
      return null;
    }

    // Try to find any assessment with marks > 0
    var anyData = dataList.stream()
        .filter(x -> "true".equalsIgnoreCase(x.getIsAttended()))
        .filter(x -> x.getMarks() != null && x.getMarks() > 0)
        .findFirst();

    if (anyData.isPresent()) {
      String result = String.format("%.1f", anyData.get().getMarks());
      log.debug("Found any available marks: {} from assessment: {}", result, anyData.get().getAssessmentName());
      return result;
    }

    return null;
  }
}
