package com.wexl.dps.reportcard;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wexl.dps.dto.Gilco11th12thHolisticReportDto;
import com.wexl.dps.managereportcard.repository.ReportCardConfigDataRepository;
import com.wexl.gilico.service.GillcoHolisticReportCard;
import com.wexl.holisticreportcards.ProgressCardService;
import com.wexl.holisticreportcards.dto.ProgressCardDto;
import com.wexl.holisticreportcards.model.ProgressCard;
import com.wexl.holisticreportcards.repository.FacilitatorStudentsRepository;
import com.wexl.holisticreportcards.repository.ProgressCardRepository;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.ResourceUtils;
import com.wexl.retail.erp.attendance.dto.StudentsAttendanceReport;
import com.wexl.retail.erp.attendance.repository.SectionAttendanceRepository;
import com.wexl.retail.guardian.model.GuardianRole;
import com.wexl.retail.metrics.reportcards.ReportCardService;
import com.wexl.retail.model.Student;
import com.wexl.retail.model.User;
import com.wexl.retail.offlinetest.dto.ReportCardDto;
import com.wexl.retail.offlinetest.model.ReportCardTemplate;
import com.wexl.retail.offlinetest.repository.LowerGradeReportCardData;
import com.wexl.retail.offlinetest.service.pointscale.PointScaleEvaluator;
import com.wexl.retail.organization.model.Organization;
import com.wexl.retail.section.domain.Section;
import com.wexl.retail.student.attributes.model.StudentAttributeValueModel;
import com.wexl.retail.subjects.model.SubjectsCategoryEnum;
import com.wexl.retail.subjects.model.SubjectsTypeEnum;
import com.wexl.retail.telegram.service.UserService;
import com.wexl.retail.term.model.TermAssessment;
import com.wexl.retail.term.repository.TermAssessmentRepository;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.antlr.v4.runtime.misc.MultiMap;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class Gilco11th12thHolisticReportCard extends BaseReportCardDefinition {

  private final ReportCardService reportCardService;
  private final GillcoHolisticReportCard gillcoHolisticReportCard;
  private final Gillco3rd5thHolisticReportCard gillco3rd5thHolisticReportCard;
  private final ProgressCardRepository progressCardRepository;
  private final ProgressCardService progressCardService;
  private final FacilitatorStudentsRepository facilitatorStudentsRepository;
  private final UserService userService;
  private final TermAssessmentRepository termAssessmentRepository;
  private final ReportCardConfigDataRepository reportCardConfigDataRepository;
  private final PointScaleEvaluator pointScaleEvaluator;
  private final SectionAttendanceRepository sectionAttendanceRepository;

  @Value("classpath:gillco-11th-12th-co-scholastic.json")
  private Resource gillco11th12thCoScholasticResource;

  @Override
  public Optional<StudentAttributeValueModel> getStudentAttributeValue(
      Student student, String key) {
    return reportCardService.getStudentAttributeValue(student, key);
  }

  @Override
  public boolean supports(ReportCardTemplate reportCardTemplate) {
    final String config = reportCardTemplate.getConfig();
    return config.equals("gilco-holistic-11th-12th-term1-report.xml");
  }

  @Override
  public Map<String, Object> build(User user, Organization org, ReportCardDto.Request request) {
    var header = gillco3rd5thHolisticReportCard.buildHolisticHeader(user);
    var body = buildBody(user, org.getSlug());
    return Map.of("header", header, "pages", List.of(1), "body", body);
  }

  public Gilco11th12thHolisticReportDto.Body buildBody(User user, String orgSlug) {
    var student = user.getStudentInfo();
    var guardians = student.getGuardians();
    var section = student.getSection();
    var mother =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.MOTHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    var father =
        guardians.isEmpty()
            ? null
            : guardians.stream()
                .filter(x -> x.getRelationType().equals(GuardianRole.FATHER))
                .findFirst()
                .map(reportCardService::getGuardianName)
                .orElse(null);
    Optional<StudentAttributeValueModel> dateOfBirthOpt =
        reportCardService.getStudentAttributeValue(student, "date_of_birth");
    var dateOfBirth = dateOfBirthOpt.map(StudentAttributeValueModel::getValue).orElse(null);
    Optional<StudentAttributeValueModel> house =
        reportCardService.getStudentAttributeValue(student, "house");
    Optional<StudentAttributeValueModel> address =
        reportCardService.getStudentAttributeValue(student, "residential_address");
    Integer age = gillcoHolisticReportCard.getAge(dateOfBirth);
    var studentData = progressCardRepository.findByStudentId(student.getId());
    List<ProgressCardDto.Competencies> competencies =
        progressCardService.getCompetencyDetails(section.getGradeSlug(), orgSlug, student);

    var data = buildTableMarks(student, orgSlug);
    Map<String, Integer> range = gillco3rd5thHolisticReportCard.getConvertedMarchToFebRange();
    var convertedFromDate = range.get("convertedFromDate");
    var convertedToDate = range.get("convertedToDate");

    var facilitatorStudentOpt = facilitatorStudentsRepository.findByStudentId(student.getId());
    var facilitator =
        facilitatorStudentOpt.isPresent() ? facilitatorStudentOpt.get().getFacilitator() : null;

    return Gilco11th12thHolisticReportDto.Body.builder()
        .name(userService.getNameByUserInfo(student.getUserInfo()))
        .className(student.getSection().getGradeName())
        .sectionName(student.getSection().getName())
        .rollNo(student.getClassRollNumber())
        .admissionNumber(student.getRollNumber())
        .house(house.map(StudentAttributeValueModel::getValue).orElse(null))
        .dateOfBirth(dateOfBirth)
        .age(age)
        .address(address.map(StudentAttributeValueModel::getValue).orElse(null))
        .fatherName(father)
        .motherName(mother)
        .allAboutMe(gillcoHolisticReportCard.buildAllAboutMe(studentData))
        .attendance(buildAttendance(student, orgSlug, convertedFromDate, convertedToDate))
        .interests(buildInterests(studentData))
        .medicalProfile(buildMedicalProfile(studentData))
        .selfAndPeerAssessments(buildSelfAndPeerAssessments(student, orgSlug))
        .scholosticMandatory(data.scholosticMandatory())
        .scholosticOptional(data.scholosticOptional())
        .scholasticAreas(buildScholasticAreas(data))
        .coScholasticAreas(buildCoScholasticAreas(data))
        .coScholasticCategories(buildCoScholasticCategories(data))
        .classRemarks(facilitator != null ? facilitator.getClassFacilitatorRemarks() : null)
        .principleRemarks(facilitator != null ? facilitator.getPrincipalRemarks() : null)
        .build();
  }

  public Gilco11th12thHolisticReportDto.TableMarks buildTableMarks(
      Student student, String orgSlug) {
    var termAssessments =
        termAssessmentRepository.getAssessmentsByTermSlugAndGradeSlug(
            Arrays.asList("t1", "t2"), student.getSection().getGradeSlug());

    var termAssessmentIds = termAssessments.stream().map(TermAssessment::getId).toList();

    var data =
        reportCardConfigDataRepository.getStudentReportByStudentAndAssessments(
            student.getId(), termAssessmentIds);
    if (Objects.isNull(data) || data.isEmpty()) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report has not been generated");
    }

    var scholasticDataList =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var optionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    var coScholasticMandatoryData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.MANDATORY.name().equals(x.getType()))
            .toList();

    var coScholasticOptionalData =
        data.stream()
            .filter(
                x ->
                    SubjectsCategoryEnum.CO_SCHOLASTIC.name().equals(x.getCategory())
                        && SubjectsTypeEnum.OPTIONAL.name().equals(x.getType()))
            .toList();

    return Gilco11th12thHolisticReportDto.TableMarks.builder()
        .scholosticMandatory(scholosticMandatory(scholasticDataList, orgSlug))
        .scholosticOptional(scholosticOptional(optionalData, orgSlug))
        .coScholosticMandatory(coScholosticMandatory(coScholasticMandatoryData, orgSlug))
        .coScholosticOptional(coScholosticOptional(coScholasticOptionalData, orgSlug))
        .build();
  }

  private List<Gilco11th12thHolisticReportDto.ScholosticOptional> scholosticOptional(
      List<LowerGradeReportCardData> optionalData, String orgSlug) {

    List<Gilco11th12thHolisticReportDto.ScholosticOptional> marksList = new ArrayList<>();

    var scholasticOptDataMap =
        optionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticOptDataMap.forEach(
        (subject, scholasticData) -> {
          var theoryMarks = getTermMarks("theory", scholasticData);
          var practicalMarks = getTermMarks("practical", scholasticData);

          marksList.add(
              Gilco11th12thHolisticReportDto.ScholosticOptional.builder()
                  .subject(subject)
                  .term1Theory(theoryMarks)
                  .term1Practical(practicalMarks)
                  .build());
        });

    return marksList;
  }

  private List<Gilco11th12thHolisticReportDto.ScholosticMandatory> scholosticMandatory(
      List<LowerGradeReportCardData> reportCardData, String orgSlug) {

    List<Gilco11th12thHolisticReportDto.ScholosticMandatory> marksList = new ArrayList<>();

    var scholasticDataMap =
        reportCardData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    scholasticDataMap.forEach(
        (subject, scholasticData) -> {
          var term1Pa = getUnitTestMarks("ut1", scholasticData);
          var term1NAP = getTheoryMarks("theory", scholasticData);
          var term1Sa = getProjectPracticalMarks("practical", scholasticData);

          var term2Pa = getUnitTestMarks("ut2", scholasticData);
          var term2NAP = getTheoryMarks("theory", scholasticData);
          var term2Sa = getProjectPracticalMarks("practical", scholasticData);

          var term1Total = sumMarks(term1Pa, term1NAP, term1Sa);
          var term2Total = sumMarks(term2Pa, term2NAP, term2Sa);

          marksList.add(
              Gilco11th12thHolisticReportDto.ScholosticMandatory.builder()
                  .subject(subject)
                  .term1Pa(term1Pa)
                  .term1NAP(term1NAP)
                  .term1Sa(term1Sa)
                  .term1Total(term1Total)
                  .term2Pa(term2Pa)
                  .term2NAP(term2NAP)
                  .term2Sa(term2Sa)
                  .term2Total(term2Total)
                  .build());
        });

    return marksList;
  }

  private List<Gilco11th12thHolisticReportDto.CoScholosticMandatory> coScholosticMandatory(
      List<LowerGradeReportCardData> coScholasticMandatoryData, String orgSlug) {

    List<Gilco11th12thHolisticReportDto.CoScholosticMandatory> marksList = new ArrayList<>();
    List<CoScholasticConfig> coScholasticConfigs =
        readCoScholasticConfig(gillco11th12thCoScholasticResource);

    var coScholasticDataMap =
        coScholasticMandatoryData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .filter(rcd -> rcd.getMarks() != null || rcd.getRemarks() != null)
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    coScholasticConfigs.forEach(
        config -> {
          String subjectName = config.getSubjectName();
          List<LowerGradeReportCardData> subjectData = coScholasticDataMap.get(subjectName);

          if (subjectData != null && !subjectData.isEmpty()) {
            var grade = calculateCoScholasticGrade(subjectData);
            marksList.add(
                Gilco11th12thHolisticReportDto.CoScholosticMandatory.builder()
                    .subject(subjectName)
                    .grade(grade)
                    .build());
          } else {
            marksList.add(
                Gilco11th12thHolisticReportDto.CoScholosticMandatory.builder()
                    .subject(subjectName)
                    .grade("")
                    .build());
          }
        });

    return marksList;
  }

  private List<Gilco11th12thHolisticReportDto.CoScholosticOptional> coScholosticOptional(
      List<LowerGradeReportCardData> coScholasticOptionalData, String orgSlug) {

    List<Gilco11th12thHolisticReportDto.CoScholosticOptional> marksList = new ArrayList<>();
    List<CoScholasticConfig> coScholasticConfigs =
        readCoScholasticConfig(gillco11th12thCoScholasticResource);

    var coScholasticDataMap =
        coScholasticOptionalData.stream()
            .sorted(Comparator.comparingLong(LowerGradeReportCardData::getSeqNo))
            .filter(rcd -> rcd.getMarks() != null || rcd.getRemarks() != null)
            .collect(
                Collectors.groupingBy(
                    LowerGradeReportCardData::getSubjectName,
                    LinkedHashMap::new,
                    Collectors.toList()));

    coScholasticConfigs.forEach(
        config -> {
          String subjectName = config.getSubjectName();
          List<LowerGradeReportCardData> subjectData = coScholasticDataMap.get(subjectName);

          if (subjectData != null && !subjectData.isEmpty()) {
            var grade = calculateCoScholasticGrade(subjectData);
            marksList.add(
                Gilco11th12thHolisticReportDto.CoScholosticOptional.builder()
                    .subject(subjectName)
                    .grade(grade)
                    .build());
          } else {
            marksList.add(
                Gilco11th12thHolisticReportDto.CoScholosticOptional.builder()
                    .subject(subjectName)
                    .grade("")
                    .build());
          }
        });

    return marksList;
  }

  public MultiMap<String, String> getReportResource(Resource resource) {
    try {
      var objectMapper = new ObjectMapper();
      return objectMapper.readValue(ResourceUtils.asString(resource), new TypeReference<>() {});
    } catch (Exception e) {
      throw new ApiException(InternalErrorCodes.INVALID_REQUEST, "Report config not found", e);
    }
  }

  private String calculateCoScholasticGrade(List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty()) {
      return "";
    }

    var data = dataList.stream().findFirst().orElse(null);
    if (data == null) {
      return "";
    }

    String grade = calculateCoScholasticGradeFromMarks(data.getMarks());
    if (grade == null || grade.isBlank()) {
      grade =
          Optional.ofNullable(data.getRemarks())
              .filter(r -> r.length() >= 2)
              .map(r -> r.substring(0, 2))
              .orElse("");
    }
    return grade;
  }

  private String calculateCoScholasticGradeFromMarks(Double marks) {
    return marks == null || marks == 0
        ? null
        : pointScaleEvaluator.evaluate("5point", BigDecimal.valueOf(marks));
  }

  private String calculateScholasticGrade(String totalMarks) {
    if (totalMarks == null || totalMarks.isEmpty() || totalMarks.equals("0.0")) {
      return "";
    }

    try {
      double marks = Double.parseDouble(totalMarks);
      return pointScaleEvaluator.evaluate("8point", BigDecimal.valueOf(marks));
    } catch (NumberFormatException e) {
      return "";
    }
  }

  private String sumMarks(String... marks) {
    double total =
        Arrays.stream(marks)
            .filter(mark -> mark != null && !mark.isEmpty() && mark.matches("\\d+(\\.\\d+)?"))
            .mapToDouble(Double::parseDouble)
            .sum();

    return String.format("%.1f", total);
  }

  private String getTermMarks(String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty() || assessmentSlug == null) {
      return null;
    }

    var matched =
        dataList.stream()
            .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
            .findFirst();

    if (matched.isEmpty()) {
      return null;
    }

    var data = matched.get();

    if ("true".equalsIgnoreCase(data.getIsAttended())) {
      Double marks = data.getMarks();
      return marks != null ? String.format("%.1f", marks) : null;
    } else {
      String remarks = data.getRemarks();
      return (remarks == null || remarks.isBlank())
          ? "AB"
          : remarks.substring(0, Math.min(2, remarks.length()));
    }
  }

  private String getPenPaperTestMarks(
      String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (dataList == null || dataList.isEmpty() || assessmentSlug == null) {
      return null;
    }

    var multipleAssessmentData =
        dataList.stream()
            .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
            .filter(
                x ->
                    x.getAssessmentSlug() != null
                        && (x.getAssessmentSlug().toLowerCase().contains("penpapertest")))
            .collect(Collectors.toList());

    if (multipleAssessmentData.isEmpty()) {
      return null;
    }

    double totalMarks = 0.0;
    int count = 0;

    for (LowerGradeReportCardData data : multipleAssessmentData) {
      if ("true".equalsIgnoreCase(data.getIsAttended()) && data.getMarks() != null) {
        totalMarks += data.getMarks();
        count++;
      }
    }

    if (count == 0) {
      return null;
    }

    double average = totalMarks / count;
    return String.format("%.1f", average);
  }

  private String getUnitTestMarks(String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (assessmentSlug == null || dataList == null || dataList.isEmpty()) {
      return null;
    }

    var matched =
        dataList.stream()
            .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
            .findFirst();

    if (matched.isEmpty()) {
      return null;
    }

    var data = matched.get();

    if ("true".equalsIgnoreCase(data.getIsAttended())) {
      Double marks = data.getMarks();
      return marks != null ? String.format("%.1f", marks) : null;
    } else {
      String remarks = data.getRemarks();
      return (remarks == null || remarks.isBlank())
          ? "AB"
          : remarks.substring(0, Math.min(2, remarks.length()));
    }
  }

  private String getTheoryMarks(String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (assessmentSlug == null || dataList == null || dataList.isEmpty()) {
      return null;
    }

    var matched =
        dataList.stream()
            .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
            .findFirst();

    if (matched.isEmpty()) {
      return null;
    }

    var data = matched.get();

    if ("true".equalsIgnoreCase(data.getIsAttended())) {
      Double marks = data.getMarks();
      return marks != null ? String.format("%.1f", marks) : null;
    } else {
      String remarks = data.getRemarks();
      return (remarks == null || remarks.isBlank())
          ? "AB"
          : remarks.substring(0, Math.min(2, remarks.length()));
    }
  }

  private String getProjectPracticalMarks(
      String assessmentSlug, List<LowerGradeReportCardData> dataList) {
    if (assessmentSlug == null || dataList == null || dataList.isEmpty()) {
      return null;
    }

    var matched =
        dataList.stream()
            .filter(x -> assessmentSlug.equalsIgnoreCase(x.getAssessmentSlug()))
            .findFirst();

    if (matched.isEmpty()) {
      return null;
    }

    var data = matched.get();

    if ("true".equalsIgnoreCase(data.getIsAttended())) {
      Double marks = data.getMarks();
      return marks != null ? String.format("%.1f", marks) : null;
    } else {
      String remarks = data.getRemarks();
      return (remarks == null || remarks.isBlank())
          ? "AB"
          : remarks.substring(0, Math.min(2, remarks.length()));
    }
  }

  public Gilco11th12thHolisticReportDto.ScholasticAreas buildScholasticAreas(
      Gilco11th12thHolisticReportDto.TableMarks tableMarks) {

    List<Gilco11th12thHolisticReportDto.Subject> subjects =
        tableMarks.scholosticMandatory() != null
            ? tableMarks.scholosticMandatory().stream()
                .map(
                    mandatory -> {
                      String grade = calculateScholasticGrade(mandatory.term1Total());

                      return Gilco11th12thHolisticReportDto.Subject.builder()
                          .name(mandatory.subject())
                          .term1UtMax("5")
                          .term1UtMarks(mandatory.term1Pa() != null ? mandatory.term1Pa() : "")
                          .term1TheoryMax("5")
                          .term1TheoryMarks(
                              mandatory.term1NAP() != null ? mandatory.term1NAP() : "")
                          .term1ProjectPracticalMax("5")
                          .term1ProjectPracticalMarks(
                              mandatory.term1Sa() != null ? mandatory.term1Sa() : "")
                          .term1TotalMax("100")
                          .term1TotalMarks(
                              mandatory.term1Total() != null ? mandatory.term1Total() : "")
                          .term2UtMax("5")
                          .term2UtMarks(mandatory.term2Pa() != null ? mandatory.term2Pa() : "")
                          .term2TheoryMax("5")
                          .term2TheoryMarks(
                              mandatory.term2NAP() != null ? mandatory.term2NAP() : "")
                          .term2ProjectPracticalMax("5")
                          .term2ProjectPracticalMarks(
                              mandatory.term2Sa() != null ? mandatory.term2Sa() : "")
                          .term2TotalMax("100")
                          .term2TotalMarks(
                              mandatory.term2Total() != null ? mandatory.term2Total() : "")
                          .build();
                    })
                .collect(Collectors.toList())
            : new ArrayList<>();

    return Gilco11th12thHolisticReportDto.ScholasticAreas.builder().subjects(subjects).build();
  }

  public List<Gilco11th12thHolisticReportDto.CoScholasticArea> buildCoScholasticAreas(
      Gilco11th12thHolisticReportDto.TableMarks tableMarks) {

    List<Gilco11th12thHolisticReportDto.CoScholasticArea> coScholasticAreas = new ArrayList<>();

    if (tableMarks.coScholosticMandatory() != null) {
      tableMarks
          .coScholosticMandatory()
          .forEach(
              mandatory -> {
                coScholasticAreas.add(
                    Gilco11th12thHolisticReportDto.CoScholasticArea.builder()
                        .name(mandatory.subject())
                        .grade(mandatory.grade() != null ? mandatory.grade() : "")
                        .build());
              });
    }

    if (tableMarks.coScholosticOptional() != null) {
      tableMarks
          .coScholosticOptional()
          .forEach(
              optional -> {
                coScholasticAreas.add(
                    Gilco11th12thHolisticReportDto.CoScholasticArea.builder()
                        .name(optional.subject())
                        .grade(optional.grade() != null ? optional.grade() : "")
                        .build());
              });
    }

    return coScholasticAreas;
  }

  public List<Gilco11th12thHolisticReportDto.CoScholasticCategory> buildCoScholasticCategories(
      Gilco11th12thHolisticReportDto.TableMarks tableMarks) {

    List<CoScholasticConfig> coScholasticConfigs =
        readCoScholasticConfig(gillco11th12thCoScholasticResource);
    List<Gilco11th12thHolisticReportDto.CoScholasticCategory> categories = new ArrayList<>();

    Map<String, List<CoScholasticConfig>> groupedConfigs =
        coScholasticConfigs.stream()
            .collect(
                Collectors.groupingBy(
                    CoScholasticConfig::getSubjectTitle, LinkedHashMap::new, Collectors.toList()));

    Map<String, String> categoryNames =
        Map.of(
            "activities", "ACTIVITIES",
            "health-and-physical-education", "HEALTH & PHYSICAL EDUCATION");

    groupedConfigs.forEach(
        (subjectTitle, configs) -> {
          List<Gilco11th12thHolisticReportDto.CoScholasticSkill> skills = new ArrayList<>();

          List<Gilco11th12thHolisticReportDto.CoScholasticArea> flatAreas =
              buildCoScholasticAreas(tableMarks);

          configs.forEach(
              config -> {
                String grade =
                    flatAreas.stream()
                        .filter(area -> area.name().equals(config.getSubjectName()))
                        .map(Gilco11th12thHolisticReportDto.CoScholasticArea::grade)
                        .findFirst()
                        .orElse("");

                skills.add(
                    Gilco11th12thHolisticReportDto.CoScholasticSkill.builder()
                        .skillName(config.getSubjectName())
                        .grade(grade)
                        .build());
              });

          String categoryName =
              categoryNames.getOrDefault(subjectTitle, subjectTitle.toUpperCase());
          categories.add(
              Gilco11th12thHolisticReportDto.CoScholasticCategory.builder()
                  .categoryName(categoryName)
                  .skills(skills)
                  .build());
        });

    return categories;
  }

  private List<CoScholasticConfig> readCoScholasticConfig(Resource resource) {
    try {
      ObjectMapper objectMapper = new ObjectMapper();
      return objectMapper.readValue(
          resource.getInputStream(),
          objectMapper
              .getTypeFactory()
              .constructCollectionType(List.class, CoScholasticConfig.class));
    } catch (IOException e) {
      throw new RuntimeException("Failed to read co-scholastic config from JSON", e);
    }
  }

  public static class CoScholasticConfig {
    private String subjectTitle;
    private String subjectName;

    public String getSubjectTitle() {
      return subjectTitle;
    }

    public void setSubjectTitle(String subjectTitle) {
      this.subjectTitle = subjectTitle;
    }

    public String getSubjectName() {
      return subjectName;
    }

    public void setSubjectName(String subjectName) {
      this.subjectName = subjectName;
    }
  }

  private Gilco11th12thHolisticReportDto.Interests buildInterests(ProgressCard studentData) {
    var interests = gillco3rd5thHolisticReportCard.buildInterests(studentData);
    return Gilco11th12thHolisticReportDto.Interests.builder()
        .reading(interests.reading())
        .dancing(interests.dancing())
        .singing(interests.singing())
        .musicalInstrument(interests.musicalInstrument())
        .sportsOrGames(interests.sportsOrGames())
        .writing(interests.writing())
        .gardening(interests.gardening())
        .yoga(interests.yoga())
        .art(interests.art())
        .craft(interests.craft())
        .cooking(interests.cooking())
        .others(interests.others())
        .specify(interests.specify())
        .build();
  }

  private Gilco11th12thHolisticReportDto.MedicalProfile buildMedicalProfile(
      ProgressCard progressCard) {
    if (progressCard == null) {
      return Gilco11th12thHolisticReportDto.MedicalProfile.builder().build();
    }
    return Gilco11th12thHolisticReportDto.MedicalProfile.builder()
        .bloodGroup(progressCard.getBloodGroup())
        .height(progressCard.getTerm1Height())
        .weight(progressCard.getTerm1Weight())
        .rightEyeSight(progressCard.getEyesightR())
        .leftEyeSight(progressCard.getEyesightL())
        .dental(progressCard.getDental())
        .build();
  }

  private List<Gilco11th12thHolisticReportDto.SelfAndPeerAssessments> buildSelfAndPeerAssessments(
      Student student, String orgSlug) {
    var section = student.getSection();

    List<ProgressCardDto.SelfAssessment> selfAssessments =
        progressCardService.getSelfAssessmentDetails(section.getGradeSlug(), orgSlug, student);

    List<ProgressCardDto.PeerAssessment> peerAssessments =
        progressCardService.getPeerAssessmentDetails(section.getGradeSlug(), orgSlug, student);

    Map<String, ProgressCardDto.SelfAssessment> selfMap =
        selfAssessments.stream()
            .collect(Collectors.toMap(ProgressCardDto.SelfAssessment::name, sa -> sa));

    Map<String, ProgressCardDto.PeerAssessment> peerMap =
        peerAssessments.stream()
            .collect(Collectors.toMap(ProgressCardDto.PeerAssessment::name, pa -> pa));

    Set<String> allSkills = new LinkedHashSet<>();
    allSkills.addAll(selfMap.keySet());
    allSkills.addAll(peerMap.keySet());

    List<Gilco11th12thHolisticReportDto.SelfAndPeerAssessments> responseList = new ArrayList<>();

    for (String skill : allSkills) {
      ProgressCardDto.SelfAssessment sa = selfMap.get(skill);
      ProgressCardDto.PeerAssessment pa = peerMap.get(skill);

      responseList.add(
          Gilco11th12thHolisticReportDto.SelfAndPeerAssessments.builder()
              .skillName(skill)
              .saTerm1(sa != null ? sa.term1() : null)
              .saTerm2(sa != null ? sa.term2() : null)
              .paTerm1(pa != null && pa.term1() != null ? pa.term1().name() : null)
              .paTerm2(pa != null && pa.term2() != null ? pa.term2().name() : null)
              .build());
    }

    return responseList;
  }

  public Gilco11th12thHolisticReportDto.Attendance buildAttendance(
      Student student, String orgSlug, Integer fromDate, Integer toDate) {
    Section section = student.getSection();
    User user = student.getUserInfo();

    List<StudentsAttendanceReport> attendanceReports =
        sectionAttendanceRepository.getStudentMonthlyAttendance(
            orgSlug, section.getId(), user.getAuthUserId(), fromDate, toDate);

    Map<Integer, Long> presentMap = new HashMap<>();
    Map<Integer, Long> workingMap = new HashMap<>();
    Map<Integer, Long> percentageMap = new HashMap<>();

    for (StudentsAttendanceReport report : attendanceReports) {
      Integer month = report.getMonthNo();
      presentMap.put(month, report.getPresentDays());
      workingMap.put(month, report.getTotalWorkingDays());
      percentageMap.put(
          month,
          Math.round((double) report.getPresentDays() / report.getTotalWorkingDays() * 100.0));
    }

    return Gilco11th12thHolisticReportDto.Attendance.builder()
        .attendingDays(buildDays("No.of Days Attended", presentMap))
        .workingDays(buildDays("No.of Working Days", workingMap))
        .percentage(buildDays("No.of Working Days", percentageMap))
        .build();
  }

  private Gilco11th12thHolisticReportDto.Days buildDays(String title, Map<Integer, Long> dataMap) {
    return Gilco11th12thHolisticReportDto.Days.builder()
        .title(title)
        .apr(dataMap.getOrDefault(4, 0L))
        .may(dataMap.getOrDefault(5, 0L))
        .jun(dataMap.getOrDefault(6, 0L))
        .july(dataMap.getOrDefault(7, 0L))
        .aug(dataMap.getOrDefault(8, 0L))
        .sep(dataMap.getOrDefault(9, 0L))
        .oct(dataMap.getOrDefault(10, 0L))
        .nov(dataMap.getOrDefault(11, 0L))
        .dec(dataMap.getOrDefault(12, 0L))
        .jan(dataMap.getOrDefault(1, 0L))
        .feb(dataMap.getOrDefault(2, 0L))
        .mar(dataMap.getOrDefault(3, 0L))
        .build();
  }
}
