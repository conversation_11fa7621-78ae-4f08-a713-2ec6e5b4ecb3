package com.wexl.dps.zerodigital.controller;

import com.wexl.dps.mlp.dto.MlpDto;
import com.wexl.dps.zerodigital.service.DpsZeroDigitalService;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/orgs/{orgSlug}/dps-zero-digital")
@RequiredArgsConstructor
public class DpsZeroDigitalController {

  private final DpsZeroDigitalService dpsZeroDigitalService;

  @GetMapping()
  public List<MlpDto.Subject> getZeroDigital(
      @PathVariable("orgSlug") String orgSlug,
      @RequestParam String boardSlug,
      @RequestParam(required = false) String childOrg,
      @RequestParam(required = false) String gradeSlug) {
    return dpsZeroDigitalService.getZeroDigital(orgSlug, boardSlug, gradeSlug, childOrg);
  }
}
