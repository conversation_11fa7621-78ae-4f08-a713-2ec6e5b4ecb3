package com.wexl.holisticreportcards.repository;

import com.wexl.holisticreportcards.model.SelfAssessment;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface SelfAssessmentRepository extends JpaRepository<SelfAssessment, Long> {
  List<SelfAssessment> findByOrgSlugAndGradeSlug(String orgSlug, String gradeSlug);
}
