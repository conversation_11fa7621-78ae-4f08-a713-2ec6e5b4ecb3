package com.wexl.saisenior.reportcard.dto;

import java.util.List;
import lombok.Builder;

public record SaiSeniorUpperGradeTerm2Dto() {

  @Builder
  public record Response(Header header, Body body) {}

  @Builder
  public record Header(
      String reportName,
      String schoolLogo,
      String schoolName,
      String academicYear,
      String admissionNumber,
      String address,
      String isoData,
      Long studentId) {}

  @Builder
  public record Body(
      String name,
      String className,
      String rollNumber,
      String admissionNumber,
      String dateOfBirth,
      String fathersName,
      String mothersName,
      FirstTable firstTable,
      SecondTable secondTable,
      ThirdTable thirdTable,
      FourthTable fourthTable,
      String daysPresent,
      String workingDays,
      String rank,
      String date,
      String generalRemark) {}

  @Builder
  public record FirstTable(List<Marks> marks) {}

  @Builder
  public record Marks(
      Long colSpan,
      String subjectName,
      String term1FA,
      Long seqNo,
      String term1SA,
      String term1IA,
      String term2FA,
      String term2SA,
      String term2IA,
      String academics,
      String overAll) {}

  @Builder
  public record SecondTable(List<SecondTableMarks> marks) {}

  @Builder
  public record SecondTableMarks(String subject, String term1Grade, String term2Grade) {}

  @Builder
  public record ThirdTable(List<ThirdTableMarks> marks) {}

  @Builder
  public record ThirdTableMarks(String name, String grade) {}

  @Builder
  public record FourthTable(
      String result,
      Double academic,
      String rank,
      String date,
      Double attendance,
      Double overall) {}
}
