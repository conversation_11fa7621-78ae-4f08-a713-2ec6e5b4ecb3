package com.wexl.pallavi.preprimary.model;

import jakarta.persistence.*;
import lombok.*;

@Entity
@Data
@NoArgsConstructor
@AllArgsConstructor
@Setter
@Getter
@Builder
@Table(name = "holistic_report_competencies_data")
public class HolisticReportCompetenciesData {

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  private Long id;

  @Column(name = "student_id")
  private Long studentId;

  @Column(name = "subject_name")
  private String subjectName;

  @Column(columnDefinition = "TEXT")
  private String skill;

  @Column(columnDefinition = "TEXT")
  private String parameter;

  private String grade;
}
